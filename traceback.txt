﻿I'm sorry, but an uncaught exception occurred.

While running game code:
  File "game/enable_persian.rpy", line 95, in script
    $ config.underlay.append(renpy.Keymap(persian_menu=Show("persian_language_menu")))
  File "game/enable_persian.rpy", line 95, in script
    $ config.underlay.append(renpy.Keymap(persian_menu=Show("persian_language_menu")))
  File "game/enable_persian.rpy", line 95, in <module>
    $ config.underlay.append(renpy.Keymap(persian_menu=Show("persian_language_menu")))
AttributeError: module 'renpy' has no attribute 'Keymap'

-- Full Traceback ------------------------------------------------------------

Full traceback:
  File "C:\Users\<USER>\Music\362\renpy\bootstrap.py", line 359, in bootstrap
    renpy.main.main()
  File "C:\Users\<USER>\Music\362\renpy\main.py", line 541, in main
    renpy.game.context().run(node)
  File "game/enable_persian.rpy", line 95, in script
    $ config.underlay.append(renpy.Keymap(persian_menu=Show("persian_language_menu")))
  File "lib/python3.9/future/utils/__init__.py", line 444, in raise_
  File "game/enable_persian.rpy", line 95, in script
    $ config.underlay.append(renpy.Keymap(persian_menu=Show("persian_language_menu")))
  File "C:\Users\<USER>\Music\362\renpy\ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:\Users\<USER>\Music\362\renpy\python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "game/enable_persian.rpy", line 95, in <module>
    $ config.underlay.append(renpy.Keymap(persian_menu=Show("persian_language_menu")))
AttributeError: module 'renpy' has no attribute 'Keymap'

Windows-10-10.0.22000 AMD64
Ren'Py 8.3.7.25031702
Becoming a Femboy 3.6.2-default
Mon Aug  4 09:46:52 2025
