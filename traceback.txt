﻿I'm sorry, but an uncaught exception occurred.

While running game code:
  File "game/enable_persian.rpy", line 50, in script
    init -998 python:
  File "game/enable_persian.rpy", line 68, in <module>
    original_text_process = getattr(config, 'text_process', None)
Exception: config.text_process is not a known configuration variable.

-- Full Traceback ------------------------------------------------------------

Full traceback:
  File "game/enable_persian.rpy", line 50, in script
    init -998 python:
  File "C:/Users\The Doctor\Music\362/renpy/ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:/Users\The Doctor\Music\362/renpy/python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "game/enable_persian.rpy", line 68, in <module>
    original_text_process = getattr(config, 'text_process', None)
  File "C:/Users\The Doctor\Music\362/renpy/defaultstore.py", line 93, in __getattr__
    raise Exception('config.%s is not a known configuration variable.' % (name))
Exception: config.text_process is not a known configuration variable.

Windows-10-10.0.22000 AMD64
Ren'Py 8.3.7.25031702
Becoming a Femboy 3.6.2-default
Mon Aug  4 09:57:23 2025
