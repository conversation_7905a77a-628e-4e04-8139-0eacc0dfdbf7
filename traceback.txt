﻿I'm sorry, but an uncaught exception occurred.

While running game code:
  File "game/enable_persian.rpy", line 60, in script
    init python:
  File "game/enable_persian.rpy", line 60, in script
    init python:
  File "game/enable_persian.rpy", line 70, in <module>
    enable_persian()
  File "game/enable_persian.rpy", line 63, in enable_persian
    original_known = renpy.known_languages
AttributeError: module 'renpy' has no attribute 'known_languages'

-- Full Traceback ------------------------------------------------------------

Full traceback:
  File "C:/Users\The Doctor\Music\362/renpy/bootstrap.py", line 359, in bootstrap
    renpy.main.main()
  File "C:/Users\The Doctor\Music\362/renpy/main.py", line 541, in main
    renpy.game.context().run(node)
  File "game/enable_persian.rpy", line 60, in script
    init python:
  File "lib/python3.9/future/utils/__init__.py", line 444, in raise_
  File "game/enable_persian.rpy", line 60, in script
    init python:
  File "C:/Users\The Doctor\Music\362/renpy/ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:/Users\The Doctor\Music\362/renpy/python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "game/enable_persian.rpy", line 70, in <module>
    enable_persian()
  File "game/enable_persian.rpy", line 63, in enable_persian
    original_known = renpy.known_languages
AttributeError: module 'renpy' has no attribute 'known_languages'

Windows-10-10.0.22000 AMD64
Ren'Py 8.3.7.25031702
Becoming a Femboy 3.6.2-default
Mon Aug  4 09:34:56 2025
