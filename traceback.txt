﻿I'm sorry, but an uncaught exception occurred.

While running game code:
  File "game/enable_persian.rpy", line 5, in script
    init -1000 python:
  File "game/enable_persian.rpy", line 14, in <module>
    config.rtl_text_align = 1.0  # Right align for RTL languages
Exception: config.rtl_text_align is not a known configuration variable.

-- Full Traceback ------------------------------------------------------------

Full traceback:
  File "game/enable_persian.rpy", line 5, in script
    init -1000 python:
  File "C:\Users\<USER>\Music\362\renpy\ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:\Users\<USER>\Music\362\renpy\python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "game/enable_persian.rpy", line 14, in <module>
    config.rtl_text_align = 1.0  # Right align for RTL languages
  File "C:\Users\<USER>\Music\362\renpy\defaultstore.py", line 101, in __setattr__
    raise Exception('config.%s is not a known configuration variable.' % (name))
Exception: config.rtl_text_align is not a known configuration variable.

Windows-10-10.0.22000 AMD64
Ren'Py 8.3.7.25031702
Becoming a Femboy 3.6.2-default
Mon Aug  4 09:56:08 2025
