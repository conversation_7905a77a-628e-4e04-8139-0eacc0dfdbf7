﻿I'm sorry, but an uncaught exception occurred.

While running game code:
  File "renpy/common/00images.rpy", line 55, in script
    init python:
  File "renpy/common/00images.rpy", line 55, in script
    init python:
  File "renpy/common/00images.rpy", line 58, in <module>
    _scan_images_directory()
  File "renpy/common/00images.rpy", line 50, in _scan_images_directory
    if renpy.image_exists(base, exact=True):
AttributeError: module 'renpy' has no attribute 'image_exists'

-- Full Traceback ------------------------------------------------------------

Full traceback:
  File "C:\Users\<USER>\Music\362\renpy\bootstrap.py", line 359, in bootstrap
    renpy.main.main()
  File "C:\Users\<USER>\Music\362\renpy\main.py", line 541, in main
    renpy.game.context().run(node)
  File "renpy/common/00images.rpy", line 55, in script
    init python:
  File "lib/python3.9/future/utils/__init__.py", line 444, in raise_
  File "renpy/common/00images.rpy", line 55, in script
    init python:
  File "C:\Users\<USER>\Music\362\renpy\ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:\Users\<USER>\Music\362\renpy\python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "renpy/common/00images.rpy", line 58, in <module>
    _scan_images_directory()
  File "renpy/common/00images.rpy", line 50, in _scan_images_directory
    if renpy.image_exists(base, exact=True):
AttributeError: module 'renpy' has no attribute 'image_exists'

Windows-10-10.0.22000 AMD64
Ren'Py 8.3.7.25031702
Becoming a Femboy 3.6.2-default
Mon Aug  4 09:28:53 2025
