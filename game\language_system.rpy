# سیستم پیشرفته مدیریت زبان برای Ren'Py 8.3.7
# Advanced Language Management System for Ren'Py 8.3.7
# نویسنده: AI Assistant | تاریخ: 2025

# ========== بلوک ۱: تنظیمات اولیه و کلاس‌های اصلی ==========
init -1000 python:
    import os
    import re

    class LanguageManager:
        """مدیر پیشرفته زبان‌ها با پشتیبانی کامل از RTL"""

        def __init__(self):
            # اطلاعات کامل زبان‌های پشتیبانی شده
            self.languages = {
                None: {
                    'name': 'English',
                    'native_name': 'English',
                    'rtl': False,
                    'font_priority': ['DejaVuSans.ttf', 'arial.ttf'],
                    'text_direction': 'ltr',
                    'number_system': 'western'
                },
                'fa': {
                    'name': 'Persian',
                    'native_name': '‏فارسی',
                    'rtl': True,
                    'font_priority': ['Amiri-Regular.ttf', 'NotoSansArabic-Regular.ttf', 'tahoma.ttf'],
                    'text_direction': 'rtl',
                    'number_system': 'persian'
                },
                'ar': {
                    'name': 'Arabic',
                    'native_name': '‏العربية',
                    'rtl': True,
                    'font_priority': ['Amiri-Regular.ttf', 'NotoSansArabic-Regular.ttf'],
                    'text_direction': 'rtl',
                    'number_system': 'arabic'
                },
                'es': {
                    'name': 'Spanish',
                    'native_name': 'Español',
                    'rtl': False,
                    'font_priority': ['DejaVuSans.ttf', 'arial.ttf'],
                    'text_direction': 'ltr',
                    'number_system': 'western'
                },
                'fr': {
                    'name': 'French',
                    'native_name': 'Français',
                    'rtl': False,
                    'font_priority': ['DejaVuSans.ttf', 'arial.ttf'],
                    'text_direction': 'ltr',
                    'number_system': 'western'
                },
                'ru': {
                    'name': 'Russian',
                    'native_name': 'Русский',
                    'rtl': False,
                    'font_priority': ['DejaVuSans.ttf', 'arial.ttf'],
                    'text_direction': 'ltr',
                    'number_system': 'western'
                }
            }

            # سیستم‌های اعداد مختلف
            self.number_systems = {
                'western': '0123456789',
                'persian': '۰۱۲۳۴۵۶۷۸۹',
                'arabic': '٠١٢٣٤٥٦٧٨٩'
            }

            # فونت‌های بارگذاری شده
            self.loaded_fonts = {}
            self._scan_fonts()

            # زبان فعلی
            self.current_language = None

            # کالبک‌های تغییر زبان
            self.change_callbacks = []

        def _scan_fonts(self):
            """اسکن و بارگذاری فونت‌های موجود"""
            all_fonts = set()
            for lang_info in self.languages.values():
                all_fonts.update(lang_info['font_priority'])

            for font in all_fonts:
                if renpy.loader.loadable(font):
                    self.loaded_fonts[font] = True
                    print(f"Font loaded: {font}")
                else:
                    self.loaded_fonts[font] = False

        def get_best_font(self, language=None):
            """بهترین فونت برای زبان مشخص شده"""
            if language is None:
                language = self.current_language

            lang_info = self.languages.get(language, self.languages[None])

            for font in lang_info['font_priority']:
                if self.loaded_fonts.get(font, False):
                    return font

            return 'DejaVuSans.ttf'  # فونت پیش‌فرض

        def is_rtl(self, language=None):
            """بررسی RTL بودن زبان"""
            if language is None:
                language = self.current_language
            return self.languages.get(language, {}).get('rtl', False)

        def get_language_info(self, language=None):
            """دریافت اطلاعات کامل زبان"""
            if language is None:
                language = self.current_language
            return self.languages.get(language, self.languages[None])

        def convert_numbers(self, text, target_system=None):
            """تبدیل اعداد به سیستم عددی مناسب"""
            if not isinstance(text, str):
                return text

            if target_system is None:
                lang_info = self.get_language_info()
                target_system = lang_info.get('number_system', 'western')

            if target_system == 'western':
                return text

            western_digits = self.number_systems['western']
            target_digits = self.number_systems.get(target_system, western_digits)

            result = text
            for i, digit in enumerate(western_digits):
                result = result.replace(digit, target_digits[i])

            return result

        def process_text(self, text):
            """پردازش کامل متن برای زبان فعلی"""
            if not isinstance(text, str):
                return text

            lang_info = self.get_language_info()

            # تبدیل اعداد
            if lang_info.get('number_system') != 'western':
                text = self.convert_numbers(text, lang_info['number_system'])

            # پردازش RTL (در صورت نیاز)
            if lang_info.get('rtl', False):
                # اضافه کردن نشانگر RTL در ابتدای متن
                if not text.startswith('‏'):
                    text = '‏' + text

            return text

        def change_language(self, language, force_restart=True):
            """تغییر زبان با پردازش کامل"""
            old_language = self.current_language
            self.current_language = language

            # تنظیم RTL
            config.rtl = self.is_rtl(language)

            # تنظیم زبان پیش‌فرض برای شکل‌دهی متن
            lang_code = language if language else 'en'
            style.default.language = lang_code

            # تنظیم فونت
            best_font = self.get_best_font(language)
            style.default.font = best_font

            # فراخوانی کالبک‌ها
            for callback in self.change_callbacks:
                try:
                    callback(old_language, language)
                except Exception as e:
                    print(f"Language change callback error: {e}")

            # تغییر زبان اصلی Ren'Py
            try:
                renpy.change_language(language)
            except Exception as e:
                print(f"Ren'Py language change error: {e}")

            # راه‌اندازی مجدد تعامل
            if force_restart:
                renpy.restart_interaction()

            print(f"Language changed from {old_language} to {language}")
            return True

        def add_change_callback(self, callback):
            """اضافه کردن کالبک برای تغییر زبان"""
            self.change_callbacks.append(callback)

        def create_translation_directory(self, language):
            """ایجاد دایرکتوری ترجمه برای زبان"""
            if language is None:
                return

            tl_dir = os.path.join(config.gamedir, "tl", language)
            try:
                os.makedirs(tl_dir, exist_ok=True)

                # ایجاد فایل ترجمه پایه
                common_file = os.path.join(tl_dir, "common.rpy")
                if not os.path.exists(common_file):
                    with open(common_file, "w", encoding="utf-8") as f:
                        lang_info = self.get_language_info(language)
                        f.write(f'# Translation file for {lang_info["name"]}\n')
                        f.write(f'translate {language} strings:\n')
                        f.write('    old "New Game"\n')
                        f.write(f'    new "{lang_info["native_name"]} - بازی جدید"\n')
                        f.write('\n')

                print(f"Translation directory created for {language}")
                return True
            except Exception as e:
                print(f"Error creating translation directory: {e}")
                return False

    # ایجاد نمونه سراسری مدیر زبان
    language_manager = LanguageManager()

    # در دسترس قرار دادن برای استفاده عمومی
    store.language_manager = language_manager

# ========== بلوک ۲: سیستم‌های پردازش متن ==========
init -999 python:
    # سیستم پردازش خودکار متن
    def auto_text_processor(text, *args, **kwargs):
        """پردازشگر خودکار متن برای همه زبان‌ها"""
        if hasattr(store, 'language_manager'):
            return store.language_manager.process_text(text)
        return text

    # ثبت پردازشگر متن
    if hasattr(config, 'text_transform_callbacks'):
        config.text_transform_callbacks.append(auto_text_processor)

    # سیستم فونت خودکار
    def auto_font_updater():
        """به‌روزرسانی خودکار فونت‌ها"""
        if hasattr(store, 'language_manager'):
            current_lang = getattr(_preferences, 'language', None)
            best_font = store.language_manager.get_best_font(current_lang)

            # به‌روزرسانی فونت‌های مختلف
            style.default.font = best_font
            style.button_text.font = best_font
            style.menu_choice_button.font = best_font

            # تنظیم RTL
            config.rtl = store.language_manager.is_rtl(current_lang)

    # کالبک تغییر زبان
    def on_language_change(old_lang, new_lang):
        """کالبک تغییر زبان"""
        auto_font_updater()

        # ایجاد دایرکتوری ترجمه در صورت نیاز
        if new_lang and hasattr(store, 'language_manager'):
            store.language_manager.create_translation_directory(new_lang)

    # ثبت کالبک
    if hasattr(store, 'language_manager'):
        store.language_manager.add_change_callback(on_language_change)

# ========== بلوک ۳: رابط کاربری پیشرفته ==========
# صفحه انتخاب زبان پیشرفته
screen advanced_language_menu():
    tag language_menu
    modal True

    # پس‌زمینه تیره با انیمیشن
    add "#000000" alpha 0.0
    at transform:
        alpha 0.0
        linear 0.3 alpha 0.8

    # فریم اصلی
    frame:
        xalign 0.5
        yalign 0.5
        xsize 700
        ysize 500
        padding (40, 40)
        background "#2a2a2a"

        at transform:
            yoffset 50 alpha 0.0
            linear 0.4 yoffset 0 alpha 1.0

        vbox:
            spacing 25

            # عنوان اصلی
            hbox:
                xalign 0.5
                spacing 20

                text "🌐" size 40
                text "انتخاب زبان / Language Selection" size 32 color "#ffffff" text_align 0.5

            null height 20

            # لیست زبان‌ها
            viewport:
                xsize 620
                ysize 280
                scrollbars "vertical"
                mousewheel True

                vbox:
                    spacing 12

                    for lang_code, lang_info in language_manager.languages.items():
                        $ current_lang = getattr(_preferences, 'language', None)
                        $ is_current = (lang_code == current_lang)

                        button:
                            xsize 600
                            ysize 60

                            if is_current:
                                background "#4a9eff"
                            else:
                                background "#404040"
                                hover_background "#505050"

                            action Function(language_manager.change_language, lang_code)

                            hbox:
                                xalign 0.5
                                yalign 0.5
                                spacing 20

                                # آیکون جهت متن
                                if lang_info['rtl']:
                                    text "◄" size 24 color "#ffaa00"
                                else:
                                    text "►" size 24 color "#00aaff"

                                # نام زبان
                                vbox:
                                    spacing 5
                                    text lang_info['native_name'] size 22 color "#ffffff"
                                    text lang_info['name'] size 16 color "#cccccc"

                                # نشانگر زبان فعلی
                                if is_current:
                                    text "✓" size 28 color "#00ff00"

            null height 20

            # اطلاعات زبان فعلی
            frame:
                xsize 600
                padding (20, 15)
                background "#1a1a1a"

                $ current_info = language_manager.get_language_info(getattr(_preferences, 'language', None))
                $ current_font = language_manager.get_best_font()

                vbox:
                    spacing 8
                    text f"زبان فعلی: {current_info['native_name']}" size 18 color "#ffffff"
                    text f"جهت متن: {'راست به چپ' if current_info['rtl'] else 'چپ به راست'}" size 16 color "#aaaaaa"
                    text f"فونت: {current_font}" size 16 color "#aaaaaa"

            null height 15

            # دکمه‌های کنترل
            hbox:
                xalign 0.5
                spacing 20

                textbutton "تست فونت / Font Test":
                    action Show("font_test_screen")
                    text_size 18
                    xsize 200

                textbutton "بستن / Close":
                    action Hide("language_menu")
                    text_size 18
                    xsize 150

# صفحه تست فونت پیشرفته
screen font_test_screen():
    tag font_test
    modal True

    add "#000000aa"

    frame:
        xalign 0.5
        yalign 0.5
        xsize 900
        ysize 700
        padding (50, 50)

        vbox:
            spacing 25

            text "🔤 تست فونت و رندرینگ متن / Font & Text Rendering Test" size 28 text_align 0.5 xalign 0.5

            null height 20

            viewport:
                xsize 800
                ysize 500
                scrollbars "vertical"

                vbox:
                    spacing 20

                    # تست متن‌های مختلف
                    for lang_code, lang_info in language_manager.languages.items():
                        frame:
                            xsize 750
                            padding (25, 20)
                            background "#2a2a2a"

                            vbox:
                                spacing 15

                                text f"{lang_info['native_name']} ({lang_info['name']})" size 22 color "#ffaa00"

                                # نمونه متن
                                if lang_code == 'fa':
                                    text "‏سلام! این یک متن نمونه فارسی است. ۱۲۳۴۵۶۷۸۹۰" size 18
                                    text "‏بسم‌الله الرحمن الرحیم - حروف پیوسته" size 18
                                elif lang_code == 'ar':
                                    text "‏مرحبا! هذا نص تجريبي باللغة العربية. ١٢٣٤٥٦٧٨٩٠" size 18
                                elif lang_code == 'ru':
                                    text "Привет! Это образец русского текста. 1234567890" size 18
                                elif lang_code == 'es':
                                    text "¡Hola! Este es un texto de muestra en español. 1234567890" size 18
                                elif lang_code == 'fr':
                                    text "Bonjour! Ceci est un exemple de texte français. 1234567890" size 18
                                else:
                                    text "Hello! This is a sample English text. 1234567890" size 18

                                # اطلاعات فنی
                                $ font_name = language_manager.get_best_font(lang_code)
                                text f"Font: {font_name} | RTL: {lang_info['rtl']} | Numbers: {lang_info['number_system']}" size 14 color "#888888"

            null height 20

            textbutton "بازگشت / Back":
                action Hide("font_test_screen")
                text_size 20
                xalign 0.5

# ========== بلوک ۴: کلیدهای میانبر و دستورات ==========
init -998 python:
    # تنظیم کلیدهای میانبر
    config.keymap['language_menu'] = ['shift_L', 'shift_l']
    config.keymap['font_test'] = ['shift_F', 'shift_f']

    # دستورات کنسول پیشرفته
    def console_change_language(lang_code):
        """تغییر زبان از طریق کنسول"""
        if hasattr(store, 'language_manager'):
            return store.language_manager.change_language(lang_code)
        return False

    def console_show_language_info():
        """نمایش اطلاعات زبان فعلی"""
        if hasattr(store, 'language_manager'):
            info = store.language_manager.get_language_info()
            print(f"Current Language: {info['native_name']} ({info['name']})")
            print(f"RTL: {info['rtl']}")
            print(f"Font: {store.language_manager.get_best_font()}")
        return True

    # ثبت دستورات کنسول
    config.console_commands.update({
        'lang': 'console_change_language',
        'persian': 'console_change_language("fa")',
        'english': 'console_change_language(None)',
        'arabic': 'console_change_language("ar")',
        'spanish': 'console_change_language("es")',
        'french': 'console_change_language("fr")',
        'russian': 'console_change_language("ru")',
        'langinfo': 'console_show_language_info()',
        'langmenu': 'renpy.show_screen("advanced_language_menu")',
        'fonttest': 'renpy.show_screen("font_test_screen")'
    })

# ========== بلوک ۵: اتصال به سیستم بازی ==========
# برچسب‌های دسترسی سریع
label show_language_menu:
    show screen advanced_language_menu
    return

label test_language_system:
    "🌐 سیستم مدیریت زبان فعال شد!"
    "‏سلام! سیستم زبان فارسی کار می‌کند."
    "Current language: [language_manager.get_language_info()['native_name']]"
    menu:
        "تغییر به فارسی / Change to Persian":
            $ language_manager.change_language('fa')
            "‏زبان به فارسی تغییر کرد! اعداد: ۱۲۳۴"

        "Change to English":
            $ language_manager.change_language(None)
            "Language changed to English! Numbers: 1234"

        "نمایش منوی زبان / Show Language Menu":
            call show_language_menu

    return

# راه‌اندازی خودکار
init -997 python:
    # راه‌اندازی اولیه سیستم
    def initialize_language_system():
        """راه‌اندازی اولیه سیستم زبان"""
        if hasattr(store, 'language_manager'):
            # تنظیم زبان اولیه
            initial_lang = getattr(_preferences, 'language', None)
            store.language_manager.current_language = initial_lang

            # اعمال تنظیمات اولیه
            config.rtl = store.language_manager.is_rtl(initial_lang)
            style.default.font = store.language_manager.get_best_font(initial_lang)

            print("🌐 Advanced Language System initialized successfully!")
            print(f"Current language: {store.language_manager.get_language_info()['native_name']}")
            print("Available shortcuts:")
            print("  Shift+L: Language Menu")
            print("  Shift+F: Font Test")
            print("Console commands: persian, english, langmenu, fonttest")

    # فراخوانی راه‌اندازی
    initialize_language_system()

# پیام تکمیل
init -996 python:
    print("=" * 60)
    print("🚀 ADVANCED LANGUAGE SYSTEM LOADED SUCCESSFULLY!")
    print("🌐 Persian, Arabic, and RTL support enabled")
    print("⌨️  Shortcuts: Shift+L (menu), Shift+F (font test)")
    print("💻 Console: persian, english, langmenu, fonttest")
    print("=" * 60)
