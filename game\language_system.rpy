# language_system.rpy
# Core language switching, RTL support, and text reordering for Persian and other languages.

init python:
    # Required for correct bidirectional rendering of RTL text.
    try:
        from bidi.algorithm import get_display
    except ImportError:
        # Fallback: simple identity if python-bidi not installed
        def get_display(text): return text

    # Define supported languages and their directionality
    LANGUAGE_INFO = {
        'fa': {'rtl': True},
        'ar': {'rtl': True},
        'he': {'rtl': True},
        None: {'rtl': False},
        'en': {'rtl': False},
        'es': {'rtl': False},
        'fr': {'rtl': False},
        'ru': {'rtl': False},
    }

    def is_rtl(lang):
        info = LANGUAGE_INFO.get(lang, {'rtl': False})
        return info['rtl']

    # Hook into Ren'Py language change
    _original_change = renpy.change_language
    def change_language(lang, **kwargs):
        # Call original language change
        rv = _original_change(lang, **kwargs)
        # Set RTL mode
        config.rtl = is_rtl(lang)
        # Force reflow of text
        renpy.exports.restart_interaction()
        return rv
    renpy.change_language = change_language

    # Transform callback to reorder text for RTL languages
    def reorder_text(txt, *args, **kwargs):
        lang = _preferences.language
        if is_rtl(lang) and isinstance(txt, str):
            return get_display(txt)
        return txt

    # Register transform callback for dialogue, menus, subtitles
    config.text_transform_callbacks.append(reorder_text)

    # Persian-specific number formatting
    def format_numbers(txt):
        eng2pers = {'0':'۰','1':'۱','2':'۲','3':'۳','4':'۴','5':'۵','6':'۶','7':'۷','8':'۸','9':'۹'}
        return ''.join(eng2pers.get(c, c) for c in txt)

    def number_transform(txt, *args, **kwargs):
        if _preferences.language == 'fa':
            return format_numbers(txt)
        return txt
    config.text_transform_callbacks.append(number_transform)

    # Font replacement mapping for Persian
    PERSIAN_FONTS = [
        "Amiri-Regular.ttf",
        "NotoSansArabic-Regular.ttf",
        "tahoma.ttf",
        "arial.ttf",
        "DejaVuSans.ttf"
    ]
    preferred = next((f for f in PERSIAN_FONTS if renpy.loader.loadable(f)), "DejaVuSans.ttf")
    # Apply font when language is Persian
    def font_callback(st, st2):
        if _preferences.language == 'fa':
            st.font = preferred
        return st
    config.build_styles_callbacks.append(font_callback)

# Screen and UI shortcuts
label show_language_menu:
    call screen language_menu
    return

screen language_menu():
    tag language_menu
    modal True
    add "#00000088"
    frame:
        xalign 0.5 yalign 0.5 xsize 400 ysize 300
        vbox:
            spacing 15 xalign 0.5
            text "Select Language / انتخاب زبان" size 26
            for code, info in LANGUAGE_INFO.items():
                # Skip None => English default
                label = code or 'en'
                textbutton label.capitalize():
                    action [Function(change_language, code), Hide('language_menu')]
                    xsize 300 text_size 20
            null height 10
            textbutton "Close / بستن":
                action Hide('language_menu')
                xsize 200 text_size 18

# Shortcut keys
init python:
    # Shift+L to open language menu
    config.keymap['language_menu'] = ['shift_L']

# Quick console commands
init python:
    config.console_commands['lang'] = "change_language"
