# Simple Persian RTL Support
# Safe implementation without complex callbacks

init python:
    # Simple Persian text formatter
    def format_persian(text):
        """Format Persian text for better display"""
        if not isinstance(text, str):
            return text
        
        # Check if text contains Persian characters
        has_persian = any('\u0600' <= char <= '\u06FF' for char in text)
        
        if has_persian:
            # Add RTL mark for Persian text
            return '\u200F' + text  # Right-to-Left Mark
        
        return text

# Make it available globally
define p = format_persian