2025-08-04 05:31:26 UTC
Windows-10-10.0.22000
Ren'Py 8.3.7.25031702

Early init took 0.40s
Loading error handling took 0.12s
Loading script took 2.71s
Loading save slot metadata took 0.05s
Loading persistent took 0.01s
Set script version to: (8, 3, 7)
Persian language support initialized successfully for Ren'Py 8.3.7
Preferred font: DejaVuSans.ttf
RTL enabled: True
Default language: fa
Running init code took 0.21s
Loading analysis data took 0.04s
Analyze and compile ATL took 0.02s
Reloading save slot metadata took 0.03s
Index archives took 0.00s
Dump and make backups took 0.00s
Cleaning cache took 0.00s
Making clean stores took 0.00s
Initial gc took 0.11s
DPI scale factor: 1.500000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: b"Couldn't load nvlib." (can be ignored)
Creating interface object took 0.00s
Cleaning stores took 0.00s
Init translation took 0.00s
Build styles took 0.00s
Load screen analysis took 0.00s
Analyze screens took 0.02s
Save screen analysis took 0.04s
Prepare screens took 0.14s
Save pyanalysis. took 0.03s
Save bytecode. took 0.08s
Running _start took 0.00s
