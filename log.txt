2025-08-04 05:47:17 UTC
Windows-10-10.0.22000
Ren'Py 8.3.7.25031702

Early init took 0.39s
Loading error handling took 0.11s
Loading script took 2.67s
Loading save slot metadata took 0.06s
Loading persistent took 0.00s
Set script version to: (8, 3, 7)
Font loaded: DejaVuSans.ttf
🌐 Advanced Language System initialized successfully!
Current language: English
Available shortcuts:
  Shift+L: Language Menu
  Shift+F: Font Test
Console commands: persian, english, langmenu, fonttest
============================================================
🚀 ADVANCED LANGUAGE SYSTEM LOADED SUCCESSFULLY!
🌐 Persian, Arabic, and RTL support enabled
⌨️  Shortcuts: Shift+L (menu), Shift+F (font test)
💻 Console: persian, english, langmenu, fonttest
============================================================
Running init code took 0.22s
Loading analysis data took 0.03s
Analyze and compile ATL took 0.02s
Reloading save slot metadata took 0.03s
Index archives took 0.00s
Dump and make backups took 0.02s
Cleaning cache took 0.00s
Making clean stores took 0.00s
Initial gc took 0.10s
DPI scale factor: 1.500000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: b"Couldn't load nvlib." (can be ignored)
Creating interface object took 0.00s
Cleaning stores took 0.00s
Init translation took 0.00s
Build styles took 0.00s
Load screen analysis took 0.00s
Analyze screens took 0.02s
Save screen analysis took 0.04s
Prepare screens took 0.14s
Save pyanalysis. took 0.03s
Save bytecode. took 0.07s
Running _start took 0.00s
Interface start took 0.65s

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: "b'ATI Technologies Inc.'"
Renderer: b'AMD Radeon(TM) Graphics'
Version: b'4.6.0 Compatibility Profile Context 22.20.42.221019'
Display Info: None
Screen sizes: virtual=(1920, 1080) physical=(1920, 1000) drawable=(1920, 1000)
Could not open 'cache/shaders.txt':
Maximum texture size: 4096x4096
Screen sizes: virtual=(1920, 1080) physical=(1920, 1000) drawable=(1920, 1000)
Could not open 'cache/shaders.txt':
Maximum texture size: 4096x4096
