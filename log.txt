2025-08-04 05:33:08 UTC
Windows-10-10.0.22000
Ren'Py 8.3.7.25031702

Early init took 0.39s
Loading error handling took 0.12s
Loading script took 2.62s
Loading save slot metadata took 0.05s
Loading persistent took 0.00s
Set script version to: (8, 3, 7)
Persian language support initialized successfully for Ren'Py 8.3.7
Preferred font: DejaVuSans.ttf
RTL enabled: True
Default language: fa
Running init code took 0.23s
Loading analysis data took 0.03s
Analyze and compile ATL took 0.02s
Reloading save slot metadata took 0.04s
Index archives took 0.00s
Dump and make backups took 0.00s
Cleaning cache took 0.00s
Making clean stores took 0.00s
Initial gc took 0.10s
DPI scale factor: 1.500000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: b"Couldn't load nvlib." (can be ignored)
Creating interface object took 0.00s
Cleaning stores took 0.00s
Init translation took 0.00s
Build styles took 0.00s
Load screen analysis took 0.00s
Analyze screens took 0.03s
Save screen analysis took 0.03s
Prepare screens took 0.13s
Save pyanalysis. took 0.00s
Save bytecode. took 0.09s
Running _start took 0.00s
