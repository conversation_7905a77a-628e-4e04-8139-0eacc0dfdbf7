2025-08-04 05:27:20 UTC
Windows-10-10.0.22000
Ren'Py 8.3.7.25031702

Early init took 0.42s
Loading error handling took 0.13s
Loading script took 2.69s
Loading save slot metadata took 0.05s
Loading persistent took 0.00s
Set script version to: (8, 3, 7)

Full traceback:
  File "game/enable_persian.rpy", line 50, in script
    init -998 python:
  File "C:/Users\The Doctor\Music\362/renpy/ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:/Users\The Doctor\Music\362/renpy/python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "game/enable_persian.rpy", line 68, in <module>
    original_text_process = getattr(config, 'text_process', None)
  File "C:/Users\The Doctor\Music\362/renpy/defaultstore.py", line 93, in __getattr__
    raise Exception('config.%s is not a known configuration variable.' % (name))
Exception: config.text_process is not a known configuration variable.

While running game code:
  File "game/enable_persian.rpy", line 50, in script
    init -998 python:
  File "game/enable_persian.rpy", line 68, in <module>
    original_text_process = getattr(config, 'text_process', None)
Exception: config.text_process is not a known configuration variable.
DPI scale factor: 1.500000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: b"Couldn't load nvlib." (can be ignored)
Interface start took 0.17s

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: "b'ATI Technologies Inc.'"
Renderer: b'AMD Radeon(TM) Graphics'
Version: b'4.6.0 Compatibility Profile Context 22.20.42.221019'
Display Info: None
Screen sizes: virtual=(1920, 1080) physical=(1920, 1000) drawable=(1920, 1000)
Could not open 'cache/shaders.txt':
Maximum texture size: 4096x4096
Screen sizes: virtual=(1920, 1080) physical=(1920, 1000) drawable=(1920, 1000)
Could not open 'cache/shaders.txt':
Maximum texture size: 4096x4096
