2025-08-04 05:46:13 UTC
Windows-10-10.0.22000
Ren'Py 8.3.7.25031702

Early init took 0.40s
Loading error handling took 0.12s
Loading script took 2.72s
Loading save slot metadata took 0.05s
Loading persistent took 0.00s
Set script version to: (8, 3, 7)
Font loaded: DejaVuSans.ttf

Full traceback:
  File "game/language_system.rpy", line 234, in script
    init -999 python:
  File "C:/Users\The Doctor\Music\362/renpy/ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:/Users\The Doctor\Music\362/renpy/python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "game/language_system.rpy", line 243, in <module>
    if hasattr(config, 'text_transform_callbacks'):
  File "C:/Users\The Doctor\Music\362/renpy/defaultstore.py", line 93, in __getattr__
    raise Exception('config.%s is not a known configuration variable.' % (name))
Exception: config.text_transform_callbacks is not a known configuration variable.

While running game code:
  File "game/language_system.rpy", line 234, in script
    init -999 python:
  File "game/language_system.rpy", line 243, in <module>
    if hasattr(config, 'text_transform_callbacks'):
Exception: config.text_transform_callbacks is not a known configuration variable.
DPI scale factor: 1.500000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: b"Couldn't load nvlib." (can be ignored)
Interface start took 0.15s

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: "b'ATI Technologies Inc.'"
Renderer: b'AMD Radeon(TM) Graphics'
Version: b'4.6.0 Compatibility Profile Context 22.20.42.221019'
Display Info: None
Screen sizes: virtual=(1920, 1080) physical=(1920, 1000) drawable=(1920, 1000)
Could not open 'cache/shaders.txt':
Maximum texture size: 4096x4096
Screen sizes: virtual=(1920, 1080) physical=(1920, 1000) drawable=(1920, 1000)
Could not open 'cache/shaders.txt':
Maximum texture size: 4096x4096
