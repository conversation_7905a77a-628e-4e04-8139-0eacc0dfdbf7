2025-08-03 19:04:41 UTC
Windows-10-10.0.22000
Ren'Py 8.3.7.25031702

Early init took 0.40s
Loading error handling took 0.12s
Loading script took 2.73s
Loading save slot metadata took 0.06s
Loading persistent took 0.00s
Set script version to: (8, 3, 7)

Full traceback:
  File "renpy/common/00images.rpy", line 55, in script
    init python:
  File "C:\Users\<USER>\Music\362\renpy\ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:\Users\<USER>\Music\362\renpy\python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "renpy/common/00images.rpy", line 58, in <module>
    _scan_images_directory()
  File "renpy/common/00images.rpy", line 50, in _scan_images_directory
    if renpy.has_image(base, exact=True):
AttributeError: module 'renpy' has no attribute 'has_image'

While running game code:
  File "renpy/common/00images.rpy", line 55, in script
    init python:
  File "renpy/common/00images.rpy", line 58, in <module>
    _scan_images_directory()
  File "renpy/common/00images.rpy", line 50, in _scan_images_directory
    if renpy.has_image(base, exact=True):
AttributeError: module 'renpy' has no attribute 'has_image'
DPI scale factor: 1.500000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: b"Couldn't load nvlib." (can be ignored)
Interface start took 0.16s

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: "b'ATI Technologies Inc.'"
Renderer: b'AMD Radeon(TM) Graphics'
Version: b'4.6.0 Compatibility Profile Context 22.20.42.221019'
Display Info: None
Screen sizes: virtual=(1920, 1080) physical=(1920, 1000) drawable=(1920, 1000)
Could not open 'cache/shaders.txt':
Maximum texture size: 4096x4096
While handling exception:
Traceback (most recent call last):
  File "C:\Users\<USER>\Music\362\renpy\execution.py", line 599, in run
    node.execute()
  File "C:\Users\<USER>\Music\362\renpy\ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:\Users\<USER>\Music\362\renpy\python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "renpy/common/00images.rpy", line 58, in <module>
    _scan_images_directory()
  File "renpy/common/00images.rpy", line 50, in _scan_images_directory
    if renpy.has_image(base, exact=True):
AttributeError: module 'renpy' has no attribute 'has_image'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Music\362\renpy\display\error.py", line 140, in report_exception
    renpy.game.invoke_in_new_context(
  File "C:\Users\<USER>\Music\362\renpy\game.py", line 303, in invoke_in_new_context
    return callable(*args, **kwargs)
  File "C:\Users\<USER>\Music\362\renpy\display\error.py", line 48, in call_exception_screen
    return renpy.ui.interact(mouse="screen", type="screen", suppress_overlay=True, suppress_underlay=True)
  File "C:\Users\<USER>\Music\362\renpy\ui.py", line 301, in interact
    rv = renpy.game.interface.interact(roll_forward=roll_forward, **kwargs)
  File "C:\Users\<USER>\Music\362\renpy\display\core.py", line 2204, in interact
    i()
  File "renpy/common/00sideimage.rpy", line 63, in _side_per_interact
    new = renpy.get_side_image(side_image_prefix_tag, image_tag=side_image_tag, not_showing=config.side_image_only_not_showing)
AttributeError: module 'renpy' has no attribute 'get_side_image'

Full traceback:
  File "C:\Users\<USER>\Music\362\renpy\bootstrap.py", line 359, in bootstrap
    renpy.main.main()
  File "C:\Users\<USER>\Music\362\renpy\main.py", line 541, in main
    renpy.game.context().run(node)
  File "renpy/common/00images.rpy", line 55, in script
    init python:
  File "lib/python3.9/future/utils/__init__.py", line 444, in raise_
  File "renpy/common/00images.rpy", line 55, in script
    init python:
  File "C:\Users\<USER>\Music\362\renpy\ast.py", line 834, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "C:\Users\<USER>\Music\362\renpy\python.py", line 1187, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "renpy/common/00images.rpy", line 58, in <module>
    _scan_images_directory()
  File "renpy/common/00images.rpy", line 50, in _scan_images_directory
    if renpy.has_image(base, exact=True):
AttributeError: module 'renpy' has no attribute 'has_image'

While running game code:
  File "renpy/common/00images.rpy", line 55, in script
    init python:
  File "renpy/common/00images.rpy", line 55, in script
    init python:
  File "renpy/common/00images.rpy", line 58, in <module>
    _scan_images_directory()
  File "renpy/common/00images.rpy", line 50, in _scan_images_directory
    if renpy.has_image(base, exact=True):
AttributeError: module 'renpy' has no attribute 'has_image'
