# Simple RTL Fix for Persian Text in Ren'Py
# This provides a practical solution for RTL text display

init python:
    # Simple RTL text handling
    def persian_rtl(text):
        """
        Simple function to handle Persian RTL text
        This is a basic implementation that works with most cases
        """
        if not isinstance(text, str) or not text.strip():
            return text
            
        # Check if text contains Persian characters
        persian_chars = any('\u0600' <= char <= '\u06FF' for char in text)
        
        if persian_chars:
            # For Persian text, we use Unicode RTL markers
            return '\u202E' + text + '\u202C'  # RTL override + text + pop directional formatting
        
        return text

# Define a custom text function for Persian
define persian_text = persian_rtl

# Create Persian-specific styles
init:
    style persian_style:
        font "tahoma.ttf"
        
    style persian_button:
        font "tahoma.ttf"
        
    style persian_label:
        font "tahoma.ttf"