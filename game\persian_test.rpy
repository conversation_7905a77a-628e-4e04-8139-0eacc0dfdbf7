# Persian Font and RTL Test Screen
screen persian_test():
    modal True
    frame:
        xalign 0.5
        yalign 0.5
        xsize 700
        ysize 500
        vbox:
            spacing 15
            text "‏تست فونت و RTL فارسی" size 28 color "#ffffff" font "tahoma.ttf" text_align 0.5 xalign 0.5
            null height 10
            
            # Test different text alignments
            text "‏متن راست‌چین: سلام دنیا" size 20 color "#ffffff" font "tahoma.ttf" text_align 1.0 xalign 1.0
            text "‏متن وسط‌چین: سلام دنیا" size 20 color "#ffffff" font "tahoma.ttf" text_align 0.5 xalign 0.5
            text "‏متن چپ‌چین: سلام دنیا" size 20 color "#ffffff" font "tahoma.ttf" text_align 0.0 xalign 0.0
            
            null height 10
            text "‏جملات طولانی:" size 18 color "#ffff00" font "tahoma.ttf" text_align 0.5 xalign 0.5
            text "‏این یک متن طولانی فارسی است که باید به درستی نمایش داده شود" size 16 color "#ffffff" font "tahoma.ttf" text_align 1.0 xalign 1.0
            
            null height 10
            text "‏اعداد فارسی: ۱۲۳۴۵۶۷۸۹۰" size 16 color "#ffffff" font "tahoma.ttf" text_align 0.5 xalign 0.5
            text "اعداد انگلیسی: 1234567890" size 16 color "#ffffff" font "tahoma.ttf" text_align 0.5 xalign 0.5
            
            null height 10
            text "‏حروف الفبا:" size 16 color "#ffff00" font "tahoma.ttf" text_align 0.5 xalign 0.5
            text "‏الف ب پ ت ث ج چ ح خ د ذ ر ز ژ س ش ص ض ط ظ ع غ ف ق ک گ ل م ن و ه ی" size 14 color "#ffffff" font "tahoma.ttf" text_align 1.0 xalign 1.0
            
            null height 15
            hbox:
                spacing 30
                xalign 0.5
                textbutton "‏بستن" action Hide("persian_test") text_font "tahoma.ttf" text_size 18
                textbutton "Close" action Hide("persian_test") text_size 18

# Add key binding for test screen
init:
    $ config.keymap['persian_test'] = ['shift_K_t']
    $ config.underlay.append(renpy.Keymap(persian_test=Show("persian_test")))