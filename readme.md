# Документация по проекту Noname Novel

## Введение

Данная документация описывает основные системы проекта визуальной новеллы, разработанной на движке Ren'Py. Документ охватывает ключевые компоненты, их взаимодействие и примеры использования.

## 1. Система персонажа (Player)

### Обзор
Система персонажа представлена классом `Player`, который управляет всеми аспектами игрового персонажа: деньгами, статистикой, одеждой и специальными параметрами.

### Ключевые компоненты
- **Player**: Основной класс персонажа
- **PlayerStats**: Хранит и управляет статистикой персонажа
- **PlayerClothing**: Управляет одеждой персонажа
- **PlayerStretching**: Отвечает за параметры анального растяжения

### Типичное использование
```python
# Получение денег
pc.receive_money(100)

# Трата денег
pc.waste_money(50)

# Перемещение персонажа
pc.go_to(ml_mall)

# Управление квестами
pc.receive_quest(quest_instance)
```

### Проверка возможности выхода
Метод `is_able_to_go_out` проверяет, может ли персонаж выйти из текущей локации, учитывая одежду:
```python
if pc.is_able_to_go_out:
    # Код для выхода
else:
    # Ошибка/предупреждение
```

## 2. Система локаций

### Обзор
Система локаций позволяет создавать доступные для посещения места, управлять их доступностью и организовывать перемещение между ними.

### Ключевые компоненты
- **Location**: Основной класс для игровых локаций
- **MapLocation**: Представляет локацию на карте мира
- **LocationMeta**: Содержит метаданные локации (музыка, звуки и т.д.)

### Типичное использование
```python
# Создание новой локации
mall = Location("mall", __("Mall"))

# Настройка доступности локации
mall.availability = [("is_daytime()", "Магазин закрыт ночью")]

# Установка музыки для локации
mall.meta.meta["music"]["day"] = m_mall_theme
mall.meta.meta["music"]["night"] = m_mall_night

# Проверка текущей локации
if is_player_location(mall):
    # Игрок в магазине
```

### Метаданные локаций
Локации могут иметь следующие метаданные:
- `enter`: Звук при входе в локацию
- `music`: Словарь с музыкой для разного времени суток (`day` и `night`)

## 3. Система квестов

### Обзор
Система квестов позволяет создавать задания для игрока, отслеживать прогресс и выдавать награды.

### Ключевые компоненты
- **QuestEntity**: Базовый класс для квестовых сущностей
- **Quest**: Представляет полный квест, состоящий из нескольких этапов
- **QuestStep**: Представляет отдельный этап квеста

### Типичное использование
```python
# Создание квеста
q_meet_trish = Quest(
    id="q_meet_trish",
    name=__("Встреча с Триш"),
    description=__("Познакомьтесь с Триш в школьном дворе")
)

# Создание этапа квеста
qs_find_trish = QuestStep(
    name=__("Найти Триш"),
    availability="is_daytime()"
)

# Добавление этапа в квест
q_meet_trish.add_step(qs_find_trish)

# Выдача квеста игроку
pc.receive_quest(q_meet_trish)

# Завершение этапа квеста
qs_find_trish.finish()

# Проверка завершенности квеста
if q_meet_trish.done:
    # Квест завершен
```

### Состояния квестов
Квесты могут находиться в следующих состояниях:
- `NOT_RECEIVED`: Квест еще не получен
- `IN_PROGRESS`: Квест выполняется
- `DONE`: Квест завершен

### سیستم کوئست‌ها 

#### مرور کلی
سیستم کوئست‌ها به شما امکان می‌دهد ماموریت‌ها را برای بازیکن ایجاد کنید، پیشرفت را دنبال کنید و پاداش‌ها را اعطا کنید.

#### اجزای اصلی  
- **QuestEntity**: کلاس پایه برای موجودیت‌های کوئست
- **Quest**: نشان‌دهنده یک کوئست کامل شامل چندین مرحله 
- **QuestStep**: نشان‌دهنده یک مرحله جداگانه از کوئست

#### نمونه استفاده معمول
```python
# ایجاد کوئست جدید
q_meet_trish = Quest(
    id="q_meet_trish", 
    name=__("ملاقات با تریش"),
    description=__("در حیاط مدرسه با تریش ملاقات کنید")
)

# ایجاد مرحله کوئست
qs_find_trish = QuestStep(
    name=__("پیدا کردن تریش"),
    availability="is_daytime()"  
)

# اضافه کردن مرحله به کوئست
q_meet_trish.add_step(qs_find_trish)

# دادن کوئست به بازیکن
pc.receive_quest(q_meet_trish)
```

## 4. Система инвентаря и предметов

### Обзор
Система инвентаря управляет предметами, которые игрок может получить и использовать.

### Ключевые компоненты
- **InventoryItem**: Базовый класс для предметов инвентаря
- **Equipable**: Предметы, которые можно экипировать
- **Cloth**: Предметы одежды
- **Toy**: Игрушки

### Типичное использование
```python
# Создание предмета одежды
croptop = Cloth(
    perversion=20,
    attractiveness=30,
    slot="shirt",
    name=__("Кроп-топ"),
    id="croptop",
    description=__("Короткий топ, открывающий живот"),
    tags=[ClothTags.CASUAL]
)

# Экипировка предмета
croptop.equip(pc)

# Создание игрушки
small_toy = Toy(
    name=__("Маленькая игрушка"),
    id="small_toy",
    description=__("Маленькая анальная игрушка"),
    size=ToySizeType.SMALL,
    perversion=15,
    slot="buttplug"
)
```

### Слоты для предметов
Для одежды доступны следующие слоты:
- `shirt`: Верхняя одежда
- `pants`: Штаны/юбка
- `bra`: Бюстгальтер
- `panties`: Нижнее белье
- `socks`: Носки/чулки
- `shoes`: Обувь
- `necklage`: Украшения на шею
- `buttplug`: Анальная пробка

## 5. Система одежды

### Обзор
Система одежды управляет экипированными предметами одежды и их влиянием на характеристики персонажа.

### Ключевые компоненты
- **PlayerClothing**: Основной класс управления одеждой
- **ClothesSet**: Набор экипированной одежды

### Типичное использование
```python
# Экипировка предмета одежды
pc.cloth.equip(croptop)

# Снятие предмета из слота
pc.cloth.unequip("shirt")

# Снятие всей одежды
pc.cloth.unequip_full()

# Получение предмета из слота
current_shirt = pc.cloth["shirt"]

# Проверка перверсивности одежды
perversion_level = pc.cloth.perversion
```

### Влияние на характеристики
Каждый предмет одежды влияет на следующие характеристики:
- `perversion`: Уровень развратности
- `attractiveness`: Уровень привлекательности

## 6. Система галереи

### Обзор
Система галереи позволяет создавать и управлять изображениями и сценами, доступными для просмотра в галерее игры.

### Ключевые компоненты
- **GalleryImage**: Представляет изображение или сцену в галерее
- **new_create_gallery_image**: Функция для создания и регистрации изображения

### Типичное использование
```python
# Создание статичного изображения
new_create_gallery_image(
    name="trish_date_01",
    label=__("Свидание с Триш I"),
    version_prefix="0100",
    replay="D_trish_first_date",
    characters=[CharacterName.TRISH],
    tags=[GalleryTags.CASUAL]
)

# Создание динамического изображения с layeredimage
new_create_gallery_image(
    name="billy_bully_bullying",
    label=__("Преследование задиры"),
    version_prefix="0100",
    static=False,
    scenes=[
        "billy_bully_bullying_01",
        "billy_bully_bullying_02"
    ],
    replay="D_billy_bully_bullying_peeking_at_backyard",
    characters=[CharacterName.BILLY, CharacterName.JANNET],
    tags=[GalleryTags.BLOWJOB, GalleryTags.PEEKING]
)
```

### Особенности создания изображений
- Для статичных сцен (`static=True`):
  - Автоматически находит все изображения в соответствующей директории
  - Параметр `available_clothes` не требуется

- Для динамических сцен (`static=False`):
  - Необходимо указать все layeredimage в параметре `scenes`
  - Необходимо указать доступные варианты одежды в `available_clothes`

### Добавление сцены в галерею
После создания сцены нужно добавить её в список галереи:
```python
# Добавление без условий
$gallery_images.append(gi_trish_date_01)

# Добавление с условием
if is_quest_done(q_date_with_trish):
    $gallery_images.append(gi_trish_date_01)
```

> **Примечание:** Подробная информация о структуре папок для галереи изображений приведена в разделе 10 "Структура папок проекта".

## 7. Соглашения об именовании в проекте

### Обзор
Соблюдение соглашений об именовании критически важно для поддержания согласованности кода и предотвращения конфликтов при разработке проекта. В данном разделе собраны все соглашения об именовании и используемые префиксы.

### Префиксы переменных

#### Квесты и события
- **q_**: Объекты квестов (Quest)
  ```python
  $ set_variable("q_trish_exam_plan", Quest(...))
  $ q_trish_exam_plan.name = __("План подготовки к экзамену")
  ```

- **qs_**: Шаги квеста (QuestStep)
  ```python
  $ set_variable("qs_trish_exam_plan_01", QuestStep())
  $ qs_trish_exam_plan_01.name = __("Поговорить с Триш")
  ```

- **ge_**: Игровые события (GameEvent)
  ```python
  $ set_variable("ge_astra_shower_peeking_01", GameEvent("ge_astra_shower_peeking_01", __("Подглядывание за Астрой в душе")))
  ```

#### Персонажи и внешний вид
- **cd_**: Данные персонажа (CharacterData)
  ```python
  $ set_variable("cd_trish", CharacterData("cd_trish"))
  $ cd_trish.name = __("Триш")
  $ cd_trish.description = __("Школьная подруга главного героя")
  ```

- **co_**: Наряды персонажей (CharacterOutfit)
  ```python
  $ co_trish_casual = CharacterOutfit("casual", __("Повседневная"))
  $ co_trish_casual.unlocked = True
  ```

- **s_**: Позиции спрайтов (sprite_positions)
  ```python
  $ show_sprite("anna", co_anna_waitress, [s_right], dissolve)
  ```

#### Галерея
- **gi_**: Изображения галереи (GalleryImage)
  ```python
  $ new_create_gallery_image(
      name="trish_date_01",  # Создает переменную gi_trish_date_01
      label=__("Свидание с Триш"),
      version_prefix="0100",
      replay="D_trish_first_date"
  )
  ```

#### Счетчики и флаги
- **c_**: Счетчики действий или событий
  ```python
  $ c_mirina_anal = c_mirina_anal + 1
  ```

- **is_**: Флаги состояний (boolean)
  ```python
  $ is_gym_visited_today = True
  $ is_astra_shower_blowjob_just_peeked = False
  ```

- **has_**: Альтернативный способ обозначения флагов наличия
  ```python
  $ has_seen_intro = True
  ```

#### Предметы инвентаря
- **i_**: Предметы инвентаря (InventoryItem)
  ```python
  $ i_gold_necklace = InventoryItem("i_gold_necklace", __("Золотое ожерелье"), __("Красивое золотое ожерелье"))
  ```

- **cl_**: Предметы одежды (Cloth)
  ```python
  $ cl_basic_shirt = Cloth("cl_basic_shirt", __("Базовая футболка"), __("Обычная футболка"))
  ```

- **t_**: Игрушки (Toy)
  ```python
  $ t_small_toy = Toy("t_small_toy", __("Маленькая игрушка"), __("Маленькая анальная игрушка"))
  ```

### Префиксы меток (labels)

- **D_**: Диалоговые метки (скрывают интерфейс)
  ```python
  label D_trish_first_date:
      hide screen ui
      # Код диалога
  ```

- **L_**: Метки локаций и системных экранов (показывают интерфейс)
  ```python
  label L_school_classroom:
      show screen ui
      # Код локации
  ```

- **S_**: Метки для сохраненных состояний
  ```python
  label S_after_trish_date:
      # Код для восстановления состояния
  ```

- **ms_**: Метки последовательностей для персонажей (SequenceStep)
  ```python
  $ ms_trish_exam_plan = SequenceStep(
      name=__("План экзамена"),
      requirments="qs_trish_exam_plan_01.in_progress",
      label="D_trish_exam_plan"
  )
  ```

### Соглашения по форматированию имен

#### Стиль имени
- Используйте snake_case для всех имен переменных, меток и функций
  ```python
  $ trish_exam_quest = Quest(...)  # Правильно
  $ TrishExamQuest = Quest(...)    # Неправильно
  ```

#### Структура имени
- Для переменных персонажей: `{префикс}_{имя_персонажа}_{описание}`
  ```python
  $ cd_trish           # Данные персонажа Триш
  $ co_trish_casual    # Повседневный наряд Триш
  $ q_trish_exam_plan  # Квест Триш, связанный с планом экзамена
  ```

- Для переменных галереи: `gi_{сцена}_{номер}`
  ```python
  $ gi_trish_date_01   # Первая сцена свидания с Триш
  $ gi_astra_shower_01 # Первая сцена с Астрой в душе
  ```

- Для событий: `ge_{место/персонаж}_{действие}_{номер}`
  ```python
  $ ge_school_peeking_kendra_kim_blowjob  # Событие подглядывания за Кендрой и Ким в школе
  ```

### Рекомендации по соблюдению соглашений

1. **Всегда проверяйте существующий код**
   Перед созданием новой переменной просмотрите аналогичный код, чтобы убедиться, что вы следуете тем же соглашениям.

2. **Последовательно применяйте префиксы**
   Никогда не опускайте префиксы - они помогают быстро определить тип переменной.

3. **Используйте описательные имена**
   После префикса имя должно ясно указывать на назначение переменной:
   ```python
   $ q_trish_exam_plan  # Хорошо: ясно, о чем идет речь
   $ q_trish_plan       # Хуже: недостаточно специфично
   ```

4. **Избегайте сокращений**
   За исключением общепринятых сокращений, пишите полные слова:
   ```python
   $ q_trish_examination_preparation  # Хорошо
   $ q_trish_exam_prep                # Избегайте таких сокращений
   ```

5. **Проверяйте уникальность имен**
   Перед созданием новой переменной убедитесь, что имя еще не используется:
   ```python
   # Если в коде уже есть
   $ q_trish_date
   
   # НЕ создавайте еще одну с тем же именем
   $ q_trish_date = Quest(...)  # Приведет к перезаписи существующей
   ```

### Почему это важно

Соблюдение соглашений об именовании:
- Упрощает чтение и понимание кода
- Предотвращает конфликты имен переменных
- Облегчает отладку
- Ускоряет разработку новых функций
- Облегчает интеграцию обновлений
- Позволяет автоматизировать некоторые процессы разработки

### Антипаттерны и как их избегать

#### ❌ Неправильно: Игнорирование префиксов
```python
$ trish_quest = Quest(...)  # Нет префикса q_
```

#### ✅ Правильно: Использование префиксов
```python
$ q_trish_quest = Quest(...)  # Префикс q_ указывает на квест
```

#### ❌ Неправильно: Смешивание стилей
```python
$ q_TrishQuest = Quest(...)  # Смешивание snake_case и CamelCase
```

#### ✅ Правильно: Последовательный стиль
```python
$ q_trish_quest = Quest(...)  # Последовательное использование snake_case
```

#### ❌ Неправильно: Неинформативные имена
```python
$ q_t_q = Quest(...)  # Слишком короткое и неясное
```

#### ✅ Правильно: Описательные имена
```python
$ q_trish_exam_preparation = Quest(...)  # Понятно, о чем идет речь
```

#### ❌ Неправильно: Сокращения
```python
$ q_trish_exam_prep = Quest(...)  # Избегайте таких сокращений
```

#### ✅ Правильно: Полные слова
```python
$ q_trish_examination_preparation = Quest(...)  # Хорошо
```

## 8. Система обновлений и блокировок

### Структура обновлений

Обновления в игре организованы через файлы в директории `game/scripts/updates/`. Эта система позволяет модульно добавлять новый контент и переменные для каждого обновления.

#### Основные компоненты
- **XXXvariables.rpy**: Файлы обновлений, где XXX - номер версии (например, `050variables.rpy`, `0150variables.rpy`)
- **initial_variables.rpy**: Содержит инициализацию базовых переменных
- **lock_variables.rpy**: Управляет блокировками и доступностью контента

#### Архитектура файлов обновлений
Каждый файл обновления содержит метку (label) `variables_for_XXX`, которая вызывает другие метки для инициализации:

```python
# Пример файла 0150variables.rpy
label variables_for_0150:
    call trish_sequences_variables_0150
    call trish_quests_variables_0150
    return
```

Это позволяет модульно добавлять переменные для персонажей, квестов и диалогов в каждом обновлении.

#### Иерархия вызовов обновлений
В файле `script.rpy` последовательно вызываются все файлы обновлений:

```python
label update_variables:
    call initial_variables
    call variables_for_030
    call variables_for_040
    # ... дополнительные вызовы обновлений ...
    call variables_for_0150
    
    call lock_variables  # Финальная настройка блокировок
    return
```

### Система блокировок

Система блокировок (locks) управляет доступностью контента в зависимости от прогресса игрока. Файлы блокировок находятся в директории `game/scripts/locks/`.

#### Основные файлы блокировок
- **character_data_locks.rpy**: Блокировки доступа к персонажам и их одежде
- **items_locks.rpy**: Блокировки предметов инвентаря
- **location_locks.rpy**: Блокировки доступа к локациям
- **smartphone_dialogue_locks.rpy**: Блокировки телефонных диалогов
- **magazines_locks.rpy**: Блокировки доступа к журналам

#### Примеры использования блокировок

**Блокировка предметов (items_locks.rpy):**
```python
label items_locks:
    # Стартовый инвентарь
    $inventory = [
        basic_shirt,
        basic_shorts,
        basic_socks,
        basic_panties,
        basic_shoes
    ]

    # Получение новых предметов при прогрессе
    if qs_astra_research_01.done:
        $inventory.append(astra_panties)
        $inventory.remove(basic_panties)
```

**Блокировка локаций (location_locks.rpy):**
```python
label location_locks:
    # Школа
    if qs_astra_research_01.done or q_astra_research.done:
        $ school_hallway.availability = [
            ("time.is_weekend", AvailabilityErrors.DOW_AVAILABILITY_ERROR),
            ("time.tod > 1", AvailabilityErrors.TOD_AVAILABILITY_ERROR),
        ]
```

**Блокировка данных персонажей (character_data_locks.rpy):**
```python
label character_data_locks:
    # Анна
    if is_quest_done(q_cafe_work):
        $ cd_anna.unlocked = True
        $ co_anna_casual.unlocked = True
        $ co_anna_waitress.unlocked = True
```

### Файл script.rpy

Файл `script.rpy` является главным файлом сценария и содержит основную логику игры:

#### Основные компоненты
- **label start**: Точка входа в игру, инициализирует основные переменные
- **label update_variables**: Вызывает все файлы обновлений в правильном порядке
- **label L_game_start**: Настраивает начальное состояние игры после пропуска пролога

#### Интеграция обновлений
`script.rpy` последовательно вызывает все файлы обновлений, что обеспечивает правильный порядок инициализации переменных и квестов:

```python
label update_variables:
    call initial_variables
    # Серия вызовов файлов обновлений...
    call variables_for_0150
    
    call lock_variables
    return
```

#### Управление интерфейсом и диалогами
В `script.rpy` также содержится логика для управления отображением интерфейса:

```python
init -100 python:
    def label_callback(name, abnormal):
        store.current_label = name

        if store.current_label.startswith("L"):
            store.interface_state = True

        if store.current_label.startswith("D"):
            store.interface_state = False
        
    config.label_callback = label_callback
```

Метки, начинающиеся с `L_`, отображают интерфейс, а метки, начинающиеся с `D_`, скрывают интерфейс (используются для диалогов).

### Добавление нового обновления

Для добавления нового обновления необходимо:

1. Создать файл `XXXvariables.rpy` в директории `updates`
2. Определить метку `variables_for_XXX`
3. Добавить вызовы меток для инициализации новых переменных персонажей и квестов
4. Добавить вызов `call variables_for_XXX` в файл `script.rpy`
5. Обновить соответствующие файлы блокировок при необходимости

Такой подход обеспечивает модульность и легкое добавление нового контента.

## 9. Система меток (Labels) и повторного воспроизведения (Replay)

### Обзор
В проекте используется система меток (labels) для организации сценариев и повторного воспроизведения (replay) для просмотра сцен в галерее. Правильное использование меток критически важно для корректной работы галереи и повторного просмотра сцен.

### Типы меток (Labels)
В проекте используются следующие соглашения по именованию меток:

1. **D_[name]** - Диалоговые сцены, содержащие основной игровой контент и сюжетные события
   ```python
   label D_kendra_pe_lesson_1:
       scene black with fade
       show center_text _("You decide to go to your PE lesson.")
       pause
       # ... код сцены ...
   ```

2. **L_[name]** - Метки локаций, определяющие доступные в них действия и интерактивные элементы
   ```python
   label L_gym_hall:
       scene background
       call screen gym_screen
   
   screen gym_screen():
       if cd_kendra.is_here:
           imagebutton:
               idle "kendra_workout"
               hover HoverImage("kendra_workout_hover")
               action Jump("D_kendra_workout_together")
               focus_mask True
   ```

Метки с префиксом D_ обычно используются для сюжетных диалогов и событий, в то время как метки с префиксом L_ определяют поведение локаций и доступные в них интерактивные элементы.

### Использование меток для Replay
Метки для replay должны:
1. Быть самодостаточными (содержать всю логику отображения сцены)
2. Устанавливать все необходимые переменные и состояния
3. Отображать весь визуальный контент, который должен быть доступен в галерее

### Связь Replay и Галереи
В функции `new_create_gallery_image` параметр `replay` указывает на метку, которая будет воспроизведена при нажатии кнопки "Play replay" в галерее:

```python
# Создание статичного изображения с указанием метки для replay
new_create_gallery_image(
    name="kendra_pe_lesson_01",
    label=__("First PE Lesson"),
    version_prefix="0100",
    replay="D_kendra_pe_lesson_1",  # Ссылка на метку диалога, который будет воспроизведен
    characters=[CharacterName.TRISH, CharacterName.KENDRA],
    tags=[GalleryTags.CASUAL, GalleryTags.WORKOUT]
)
```

Значение параметра `replay` должно соответствовать существующей метке с префиксом D_, которая содержит диалоговую сцену, связанную с этим изображением галереи.

### Механизм работы Replay
1. При нажатии на кнопку "Play replay" в галерее вызывается функция `renpy.call_replay`
2. Запускается метка `replay_handler`, которая:
   - Инициализирует необходимые переменные и объекты
   - Восстанавливает состояние персонажа (например, его экипировку)
   - Запускает указанную метку с префиксом D_ через механизм `jump expression`

Важно отметить, что система replay полагается на существование корректных меток диалога (D_) в проекте. Если указанная метка не существует, будет вызвана ошибка при попытке воспроизведения.

### Рекомендации по работе с метками и replay
1. **Соблюдайте соглашения по префиксам меток**:
   - Используйте `D_` для диалоговых сцен и событий
   - Используйте `L_` для локаций и их интерактивных элементов
2. **Создавайте самодостаточные диалоговые метки** для replay, не зависящие от глобального состояния игры
3. **Проверяйте существование метки** перед указанием её в `replay`
4. **Избегайте зависимостей от текущего состояния игры** в метках для replay
5. **Корректно инициализируйте переменные** в начале метки для replay

### Пример правильной метки для replay
```python
label D_trish_first_date:
    # Инициализация переменных и настройка сцены
    $ pc.cloth.unequip_temp_full()
    $ pc.cloth.equip(basic_casual_shirt)
    $ pc.cloth.equip(basic_jeans)
    
    # Настройка локации
    scene bg day cafe with fade
    
    # Диалог и действия
    show trish smile at right
    t "Привет! Я так рада, что ты пришел!"
    
    show main_character smile at left
    mc "Я тоже рад тебя видеть."
    
    # ... остальной код сцены ...
    
    # Разблокировка изображения в галерее (при первом просмотре)
    if not _in_replay:
        $ gi_trish_date_01.unlock()
    
    return
```

В этом примере метка `D_trish_first_date` содержит полную сцену свидания с Триш, включая настройку одежды персонажа, фона и всего диалога. Это позволяет воспроизвести сцену в галерее в точности так, как она происходила в игре.

## 10. Система управления персонажами (NPC)

### Обзор
Система управления персонажами (NPC) позволяет создавать и управлять неигровыми персонажами, их расположением, данными и взаимодействиями с игроком. Эта система тесно интегрирована с системой локаций и временем игры.

### Ключевые компоненты
- **CharacterData**: Основной класс для управления данными NPC
- **CharacterOutfit**: Представляет наряды персонажа
- **CharacterEmplacement**: Управляет расположением персонажа в разное время суток
- **CharacterFact**: Представляет открываемую информацию о персонаже
- **Unlockable**: Базовый класс для контента, который может быть разблокирован

### Класс Unlockable
Базовый класс для всех элементов, которые могут быть разблокированы в игре.

```python
class Unlockable(object):
    _unlocked = False
    
    @property
    def unlocked(self):
        return self._unlocked
    
    @unlocked.setter
    def unlocked(self, value):
        if type(value) == bool:
            self._unlocked = value
        else:
            raise Exception("Invalid value for unlocked parameter")
```

### Класс CharacterData
Основной класс для управления данными неигровых персонажей.

```python
class CharacterData(Unlockable):
    def __init__(self, id, name="", description=""):
        self.id = id                          # Уникальный идентификатор (обычно с префиксом cd_)
        self._name = name                     # Имя персонажа
        self._description = description       # Описание персонажа
        self._sprite = id.replace("cd_", "")  # Идентификатор спрайта
        self._outfits = []                    # Список нарядов персонажа
        self._facts = []                      # Открываемые факты о персонаже
        self._statistics = {}                 # Статистика персонажа
        self._unlocked = False                # Разблокирован ли персонаж
        self._emplacement = CharacterEmplacement()  # Расположение персонажа
```

#### Ключевые свойства CharacterData
- **name**: Имя персонажа
- **description**: Описание персонажа
- **outfits**: Список нарядов персонажа
- **sprite**: Идентификатор спрайта персонажа
- **emplacement**: Расположение персонажа в разное время суток
- **is_here**: Находится ли персонаж в текущей локации игрока
- **current_location**: Текущая локация персонажа
- **current_map_location**: Текущая локация персонажа на карте

#### Создание и настройка CharacterData
При создании и настройке персонажа важно помнить, что все строки, которые будут отображаться пользователю (имя, описание и т.д.), должны быть обернуты в функцию __() для обеспечения возможности перевода.

```python
# Создание персонажа - при начальном создании можно не использовать __(), если значения будут переопределены
$ set_variable("cd_trish", CharacterData("cd_trish"))

# Настройка свойств с использованием функции __() для перевода
$ cd_trish.name = __("Триш")
$ cd_trish.description = __("Школьная подруга главного героя. Увлекается спортом.")

# Полный пример создания и настройки персонажа
label character_setup:
    $ set_variable("cd_jessica", CharacterData("cd_jessica"))
    $ cd_jessica.name = __("Джессика")
    $ cd_jessica.description = __("Дизайнер одежды, владелец франшизы, директор модельного агентства.")
    $ cd_jessica.outfits = [
        (__("Обычная"), "underwear_naked shoes_basic pants_basic shirt_basic", "basic"),
        (__("Обнаженная"), "underwear_naked shoes_naked pants_naked shirt_naked", "naked"),
        (__("Бикини"), "underwear_bikini shoes_naked pants_naked shirt_naked", "bikini")
    ]
    return
```

#### Типичное использование CharacterData
```python
# Создание персонажа
$ set_variable("cd_trish", CharacterData("cd_trish", __("Триш"), __("Школьная подруга")))

# Настройка расположения персонажа
$ cd_trish.emplacement.morning = [
    CharacterEmplacementDetails("is_school_day()", school_hallway, school_hallway_map),
    CharacterEmplacementDetails("True", "", "")
]

# Проверка, находится ли персонаж в текущей локации
if cd_trish.is_here:
    show trish smile at right
    t "Привет! Чем могу помочь?"

# Добавление факта о персонаже (пример перенесен в раздел о CharacterFact)

# Разблокировка персонажа в галерее персонажей
$ cd_trish.unlocked = True
```

### Класс CharacterOutfit
Представляет наряд или образ персонажа, который может быть разблокирован.

```python
class CharacterOutfit(Unlockable):
    def __init__(self):
        self._label = None       # Метка/название наряда
        self._attribute = None   # Атрибут для layeredimage
        self._unlocked = False   # Разблокирован ли наряд
```

#### Создание нарядов для персонажей
Наряды для персонажей создаются через свойство `outfits` класса `CharacterData`. Каждый наряд задается кортежем из трех элементов:
1. **Метка/название наряда** - текстовое описание наряда, которое будет отображаться в интерфейсе. Должно быть обернуто в функцию __() для обеспечения перевода.
2. **Атрибуты для layeredimage** - строка, содержащая список атрибутов layeredimage, разделенных пробелами. Эти атрибуты определяют, какие части одежды или тела будут отображаться.
3. **Префикс** - используется для создания переменной наряда в формате `co_{персонаж}_{префикс}`. Например, "basic" создаст переменную `co_trish_basic`.

```python
# Важно: все строки, которые будут отображаться пользователю, 
# должны быть обернуты в функцию __() для обеспечения перевода
$ cd_jessica.outfits = [
    (__("Basic"), "underwear_naked shoes_basic pants_basic shirt_basic", "basic"),
    (__("Naked"), "underwear_naked shoes_naked pants_naked shirt_naked", "naked"),
    (__("Bikini"), "underwear_bikini shoes_naked pants_naked shirt_naked", "bikini")
]

# За кулисами автоматически создаются объекты:
# co_jessica_basic, co_jessica_naked, co_jessica_bikini
```

#### Пример для сложных нарядов с комбинацией разных элементов:
```python
$ cd_james.outfits = [
    (__("Basic"), "outfit_basic", "basic"),                  # Базовая одежда
    (__("Summer"), "outfit_summer", "summer"),               # Летний вариант
    (__("Naked"), "outfit_naked pp_soft", "naked"),          # Обнаженный персонаж
    (__("Hard"), "outfit_naked pp_hard", "hard"),            # Другое состояние
]
```

#### Типичное использование CharacterOutfit
```python
# Автоматическое создание через систему outfits в CharacterData
$ cd_trish.outfits = [
    (__("Школьная форма"), "school", "school"),
    (__("Спортивная одежда"), "sport", "sport")
]

# Разблокировка наряда
$ co_trish_casual.unlocked = True

# Проверка разблокирован ли наряд
if co_trish_sport.unlocked:
    "Вы разблокировали спортивную форму Триш!"
```

### Класс CharacterEmplacement
Управляет расположением персонажа в разное время суток.

```python
class CharacterEmplacement(object):
    def __init__(self):
        # Расположение для каждого времени суток
        self.morning = [CharacterEmplacementDetails()]
        self.afternoon = [CharacterEmplacementDetails()]
        self.evening = [CharacterEmplacementDetails()]
        self.night = [CharacterEmplacementDetails()]
```

#### Класс CharacterEmplacementDetails
Детали расположения персонажа в определенное время суток.

```python
class CharacterEmplacementDetails(object):
    def __init__(self, condition="True", location="", map_location=""):
        self.condition = condition        # Условие для этого расположения
        self.location = location          # Объект локации
        self.map_location = map_location  # Объект расположения на карте
```

#### Типичное использование CharacterEmplacement
```python
# Настройка расположения персонажа утром
$ cd_trish.emplacement.morning = [
    # В школьном коридоре в школьные дни
    CharacterEmplacementDetails("is_school_day()", school_hallway, school_hallway_map),
    # Дома в другие дни
    CharacterEmplacementDetails("not is_school_day()", trish_house, trish_house_map)
]

# Настройка расположения персонажа вечером
$ cd_trish.emplacement.evening = [
    # В кафе по пятницам
    CharacterEmplacementDetails("time.day == 'friday'", cafe, cafe_map),
    # Дома в другие дни
    CharacterEmplacementDetails("True", trish_house, trish_house_map)
]
```

### Класс CharacterFact
Представляет открываемую информацию о персонаже.

```python
class CharacterFact(Unlockable):
    def __init__(self, id, text):
        self.id = id              # Уникальный идентификатор факта
        self.text = text          # Текст факта
        self._unlocked = False    # Разблокирован ли факт
    
    def reveal(self):
        # Метод для разблокировки факта
        self._unlocked = True
```

#### Типичное использование CharacterFact
```python
# Создание и добавление факта
# Важно: текст факта должен быть обернут в функцию __() для обеспечения перевода
$ fact = CharacterFact("trish_hobby", __("Триш любит читать фантастику"))
$ cd_trish.add_fact(fact)

# Несколько фактов о персонаже
$ cd_trish.add_fact(CharacterFact("trish_age", __("Триш 18 лет")))
$ cd_trish.add_fact(CharacterFact("trish_sport", __("Триш занимается плаванием уже 5 лет")))
$ cd_trish.add_fact(CharacterFact("trish_fav_food", __("Любимая еда Триш - суши")))

# Разблокировка факта
$ fact.reveal()

# Или напрямую через метод unlock
$ fact.unlocked = True
```

### Взаимодействие с системой времени
Система персонажей тесно интегрирована с системой времени игры, позволяя определять расположение персонажей в разное время суток.

```python
# Настройка разного расположения в зависимости от времени суток
$ cd_trish.emplacement.morning = [CharacterEmplacementDetails("True", l_school_classroomom, school_map)]
$ cd_trish.emplacement.afternoon = [CharacterEmplacementDetails("True", school_hallway, school_map)]
$ cd_trish.emplacement.evening = [CharacterEmplacementDetails("True", trish_house, city_map)]
$ cd_trish.emplacement.night = [CharacterEmplacementDetails("True", "", "")]  # Недоступна ночью

# Проверка доступности персонажа в зависимости от времени
if cd_trish.is_here and time.time == "evening":
    # Кастомный диалог для вечернего времени
    t "Уже вечер, давай посмотрим фильм!"
elif cd_trish.is_here:
    # Стандартный диалог для других времён суток
    t "Привет! Чем могу помочь?"
```

## 10. Структура папок проекта

Правильная организация файлов и папок является важной частью проекта и обеспечивает согласованность и удобство работы. Ниже описана общая структура папок проекта и конкретные требования к организации папок для различных подсистем.

### Общая структура проекта

```
game/
├── gui/                    # Файлы пользовательского интерфейса
├── images/                 # Общие изображения
├── screens/                # Экраны Ren'Py
└── scripts/                # Основной код игры
    ├── core/               # Ядро системы
    │   ├── character_data.rpy     # Система персонажей
    │   ├── laptop/               # Система ноутбука
    │   │   ├── gallery.rpy       # Система галереи
    │   │   └── messenger.rpy     # Система мессенджера
    │   ├── quest.rpy             # Система квестов
    │   └── time.rpy              # Система времени
    ├── data/               # Данные игры
    │   ├── gallery_images/        # Объявления изображений галереи
    │   ├── locations/             # Объявления локаций
    │   └── variables/             # Глобальные переменные
    ├── characters/         # Данные персонажей
    │   ├── trish/                # Папка для персонажа
    │   │   └── defaults.rpy      # Настройки персонажа
    │   └── kendra/               # Папка для персонажа
    └── sequences/          # Последовательности и обновления
        ├── 0100/                 # Папка версии
        ├── 0110/                 # Папка версии
        └── 0160/                 # Папка версии
            ├── events.rpy        # События версии
            ├── gallery_images.rpy # Изображения галереи
            ├── quests.rpy        # Квесты версии
            └── astra_revenge/    # Сюжетная линия
                ├── dialogues.rpy # Диалоги
                ├── images.rpy    # Определения layeredimage
                └── images/       # Изображения сцены
```

### Структура папок для галереи изображений

Функция `new_create_gallery_image` требует определенной структуры папок для правильной работы:

```
scripts/
└── sequences/
    └── {version_prefix}/
        └── {name}/
            └── images/
                ├── _thumb.webp (миниатюра изображения)
                └── [...] (все остальные изображения сцены)
```

- `{version_prefix}` - номер версии или обновления (например, "0160")
- `{name}` - уникальное имя сцены
- Внутри папки `images/` должны находиться:
  - Файл `_thumb.webp` (обязательная миниатюра для галереи)
  - Все остальные изображения сцены (либо напрямую, либо в подпапках)

#### Важные замечания:
1. Миниатюра должна называться именно `_thumb.webp` (с подчеркиванием в начале)
2. Все изображения рекомендуется сохранять в формате WebP для оптимизации размера
3. Функция автоматически найдет все файлы в папке `images/` и добавит их в сцену галереи
4. При отсутствии правильной структуры папок, изображения не будут отображаться в галерее

#### Для статичных сцен
Для статичных сцен все изображения хранятся напрямую в папке `images/`:

```
scripts/sequences/0160/mirina_time_together/images/
├── _thumb.webp
├── 01.webp
├── 02.webp
└── 03.webp
```

#### Для динамических сцен с layeredimage
Для сцен, использующих систему layeredimage, следует создавать подпапки для каждого состояния:

```
scripts/sequences/0160/astra_revenge/images/
├── _thumb.webp
├── 01/
│   ├── base.webp
│   ├── shirt_basic.webp
│   ├── shirt_casual.webp
│   └── ...
├── 02/
│   ├── base.webp
│   └── ...
└── ...
```

Соответствующие определения layeredimage должны находиться в файле `images.rpy` в папке сцены:

```python
layeredimage player astra_revenge_01:
    attribute_function ClothPicker()
    
    image_format "scripts/sequences/0160/astra_revenge/images/01/{image}.webp"
    
    always "base"
    
    # Группы и атрибуты...
```

## 11. Организация кода для новых обновлений

При разработке нового обновления рекомендуется придерживаться единой структуры организации файлов и кода. Ниже приведен эталонный пример организации, основанный на структуре обновления 0160.

### Шаги создания нового обновления

1. **Создание базовой структуры**
   - Создайте папку с номером версии в `scripts/sequences/` (например, `0170`)
   - Добавьте основные файлы: `events.rpy`, `gallery_images.rpy`, `quests.rpy`
   - Создайте подпапки для каждой сюжетной линии/сцены

2. **Настройка галереи изображений**
   - В `gallery_images.rpy` создайте метку с соответствующим номером (например, `gallery_images_0170`)
   - Используйте `new_create_gallery_image` для каждой сцены
   - Не забудьте добавить созданные изображения в галерею с помощью `add_gallery_image`

   Пример:
   ```python
   label gallery_images_0170:
       $ new_create_gallery_image(
           name="scene_name",
           label=__("Название сцены"),
           version_prefix="0170",
           replay="D_scene_name",
           characters=[CharacterName.CHARACTER]
       )

       $ add_gallery_image(gi_scene_name)

       return
   ```

3. **Создание квестов**
   - В `quests.rpy` создайте метку с соответствующим номером (например, `quests_0170`)
   - Используйте `set_variable` для создания шагов квеста и самого квеста
   - Установите все необходимые свойства квестов

   Пример:
   ```python
   label quests_0170:
       $ set_variable("qs_quest_name_01", QuestStep())
       $ qs_quest_name_01.name = __("Название шага квеста")
       $ qs_quest_name_01.reward = []

       $ set_variable("q_quest_name", Quest(id="q_quest_name", steps=[qs_quest_name_01]))
       $ q_quest_name.name = __("Название квеста")
       $ q_quest_name.description = __("Описание квеста")
       $ q_quest_name.availability = ""
       $ q_quest_name.reward = []

       return
   ```

4. **Объявление игровых событий**
   - В `events.rpy` создайте метку с соответствующим номером (например, `events_0170`)
   - Используйте `set_variable` для создания игровых событий

   Пример:
   ```python
   label events_0170:
       $ set_variable("ge_event_name", GameEvent("ge_event_name", __("Название события")))
       $ ge_event_name.reward = [
           "add_gallery_image(gi_scene_name)"
       ]

       return
   ```

5. **Создание диалогов и сценария**
   - В каждой подпапке сюжетной линии создайте файл `dialogues.rpy`
   - Создайте метку с префиксом `D_` для каждого диалога/сцены
   - Не забудьте использовать функцию `__()` для всех текстов для обеспечения перевода

   Пример:
   ```python
   label D_scene_name:
       scene black with fade
       
       "Текст сцены"
       
       # Логика сцены
       
       $ pc.go_to(location_name)  # Перемещение игрока после сцены
       
       return
   ```

6. **Настройка изображений сцены**
   - Для статичных сцен:
     - Поместите все изображения в подпапку `images/` сцены
     - Не забудьте создать миниатюру `_thumb.webp`
   
   - Для динамических сцен с layeredimage:
     - Создайте файл `images.rpy` в папке сцены
     - Объявите layeredimage для каждого состояния/варианта сцены
     - Создайте подпапки для каждого состояния в `images/`

   Пример layeredimage:
   ```python
   layeredimage player scene_name_01:
       attribute_function ClothPicker()
       
       image_format "scripts/sequences/0170/scene_name/images/01/{image}.webp"
       
       always "base"
       
       group outfit:
           attribute outfit_1 default:
               "outfit_1"
   ```

### Советы по организации кода

1. **Соблюдайте соглашения об именовании**:
   - Используйте префикс `D_` для диалоговых меток
   - Используйте префикс `q_` для квестов и `qs_` для шагов квестов
   - Используйте префикс `ge_` для игровых событий

2. **Держите файлы маленькими и специализированными**:
   - Каждый файл должен отвечать за одну конкретную функцию
   - Разделяйте большие сцены на несколько файлов при необходимости

3. **Используйте функции перевода**:
   - Все строки, отображаемые пользователю, должны быть обернуты в `__()`

4. **Структурируйте изображения логично**:
   - Для простых сцен используйте нумерацию (01.webp, 02.webp, ...)
   - Для сложных сцен с layeredimage создавайте подпапки с компонентами

5. **Тестируйте каждую сцену отдельно**:
   - Создавайте временные метки для тестирования сцен
   - Проверяйте корректность работы галереи и replay

6. **Поддерживайте актуальную документацию**:
   - Комментируйте сложные участки кода
   - Обновляйте списки переменных и событий

## 12. Заключение

Данная документация охватывает основные системы проекта и методы работы с ними. Для более детального понимания конкретных функций рекомендуется обращаться к комментариям в соответствующих файлах исходного кода.

Особое внимание стоит уделить разделам о структуре папок и организации кода для новых обновлений, так как соблюдение этих соглашений обеспечивает согласованность проекта и упрощает его поддержку.

При разработке рекомендуется придерживаться описанных соглашений и использовать специальные функции для создания переменных, работы с галереей и другими системами. Это поможет избежать конфликтов и обеспечит стабильность проекта.

Помните о необходимости использования функции __() для всех строк, которые будут отображаться пользователю, чтобы обеспечить возможность перевода игры на другие языки.