# پشتیبانی کامل از زبان فارسی برای Ren'Py 8.3.7
# Complete Persian Language Support for Ren'Py 8.3.7

# بلوک ۱: تنظیمات اصلی RTL و فارسی
init -1000 python:
    # فعال‌سازی جهت متن راست به چپ (RTL)
    config.rtl = True
    
    # تنظیم فارسی به عنوان زبان پیش‌فرض برای شکل‌دهی متن
    # این کار باعث فعال شدن اشکال صحیح حروف فارسی می‌شود
    style.default.language = "fa"

# بلوک ۲: تنظیمات فونت
init -999 python:
    # لیست فونت‌های فارسی به ترتیب اولویت
    persian_fonts = [
        "Amiri-Regular.ttf",     # بهترین فونت عربی/فارسی
        "NotoSansArabic-Regular.ttf",
        "tahoma.ttf",            # فونت پشتیبان ویندوز
        "arial.ttf",             # فونت جهانی
        "DejaVuSans.ttf"         # فونت پیش‌فرض Ren'Py
    ]
    
    # پیدا کردن بهترین فونت موجود
    preferred_font = "DejaVuSans.ttf"  # فونت پیش‌فرض
    for font in persian_fonts:
        if renpy.loader.loadable(font):
            preferred_font = font
            break
    
    # اعمال فونت به استایل پیش‌فرض
    style.default.font = preferred_font
    
    # ایجاد نقشه جایگزینی فونت جامع
    # این کار تضمین می‌کند که تمام متن‌ها از فونت فارسی استفاده کنند
    font_replacements = {}
    default_fonts = [
        "DejaVuSans.ttf", "Rubik-Regular.ttf", "Inter-Regular.ttf", 
        "Hooverville.ttf", "arial.ttf", "calibri.ttf"
    ]
    
    for font in default_fonts:
        for bold in [False, True]:
            for italic in [False, True]:
                font_replacements[(font, bold, italic)] = (preferred_font, bold, italic)
    
    config.font_replacement_map.update(font_replacements)

# بلوک ۳: پشتیبانی از زبان
init -997 python:
    # تنظیمات زبان فارسی
    def setup_persian_language():
        """راه‌اندازی پشتیبانی از زبان فارسی"""
        # اطمینان از شناسایی فارسی به عنوان زبان معتبر
        # سیستم ترجمه به طور خودکار آن را تشخیص می‌دهد اگر tl/fa/ وجود داشته باشد
        
        # تنظیم تنظیمات مخصوص فارسی
        if hasattr(config, 'language_callbacks'):
            def persian_callback():
                # هنگام تغییر به فارسی فراخوانی می‌شود
                style.default.language = "fa"
                config.rtl = True
                
            config.language_callbacks.setdefault("fa", []).append(persian_callback)
        
        # تنظیم ترجیحات پیش‌فرض فارسی
        if not hasattr(store, '_persian_setup_done'):
            store._persian_setup_done = True
            
            # ایجاد دایرکتوری زبان فارسی در صورت عدم وجود
            import os
            persian_dir = os.path.join(config.gamedir, "tl", "fa")
            if not os.path.exists(persian_dir):
                try:
                    os.makedirs(persian_dir, exist_ok=True)
                    # ایجاد فایل ترجمه پایه برای ثبت زبان
                    with open(os.path.join(persian_dir, "common.rpy"), "w", encoding="utf-8") as f:
                        f.write('# فایل ترجمه فارسی\n')
                        f.write('translate fa strings:\n')
                        f.write('    old "New Game"\n')
                        f.write('    new "بازی جدید"\n')
                except:
                    pass  # نادیده گرفتن خطاها در صورت عدم امکان ایجاد دایرکتوری
    
    setup_persian_language()

# بلوک ۴: منوی انتخاب زبان فارسی
screen persian_language_menu():
    # منوی پیشرفته انتخاب زبان فارسی
    modal True
    
    # پس‌زمینه تیره
    add "#000000aa"
    
    # فریم اصلی منو
    frame:
        xalign 0.5
        yalign 0.5
        xsize 600
        ysize 450
        padding (30, 30)
        
        vbox:
            spacing 20
            
            # عنوان
            text "‏انتخاب زبان / Language Selection" size 28 text_align 0.5 xalign 0.5 color "#ffffff"
            
            null height 20
            
            # دکمه‌های زبان
            vbox:
                spacing 15
                xalign 0.5
                
                textbutton "‏فارسی (Persian)":
                    action [Language("fa"), Hide("persian_language_menu")]
                    text_size 22
                    xalign 0.5
                    xsize 400
                    
                textbutton "English":
                    action [Language(None), Hide("persian_language_menu")]
                    text_size 22
                    xalign 0.5
                    xsize 400
                    
                textbutton "Español":
                    action [Language("es"), Hide("persian_language_menu")]
                    text_size 22
                    xalign 0.5
                    xsize 400
                    
                textbutton "Français":
                    action [Language("fr"), Hide("persian_language_menu")]
                    text_size 22
                    xalign 0.5
                    xsize 400
                    
                textbutton "Русский":
                    action [Language("ru"), Hide("persian_language_menu")]
                    text_size 22
                    xalign 0.5
                    xsize 400
            
            null height 30
            
            # راهنما
            vbox:
                spacing 8
                xalign 0.5
                
                text "‏راهنمای کلیدهای میانبر / Keyboard Shortcuts:" size 16 text_align 0.5 xalign 0.5 color "#cccccc"
                text "Shift+P: Open Language Menu" size 14 text_align 0.5 xalign 0.5 color "#aaaaaa"
                text "Shift+F: Font Test Screen" size 14 text_align 0.5 xalign 0.5 color "#aaaaaa"
                text "Escape: Close Menu" size 14 text_align 0.5 xalign 0.5 color "#aaaaaa"
            
            null height 20
            
            # دکمه بستن
            textbutton "‏بستن / Close":
                action Hide("persian_language_menu")
                text_size 20
                xalign 0.5
                xsize 200

# بلوک ۵: صفحه تست فونت
screen persian_font_test():
    # صفحه تست رندر فونت فارسی
    modal True
    
    add "#000000dd"
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 600
        padding (40, 40)
        
        vbox:
            spacing 20
            
            text "‏تست فونت فارسی / Persian Font Test" size 24 text_align 0.5 xalign 0.5
            
            null height 20
            
            # نمونه متن‌های فارسی
            vbox:
                spacing 15
                
                text "‏نمونه متن فارسی:" size 20 color "#ffaa00"
                text "‏سلام! این یک متن نمونه فارسی است." size 18
                text "‏اعداد: ۱۲۳۴۵۶۷۸۹۰" size 18
                text "‏علائم نگارشی: ؟ ! ؛ : « »" size 18
                text "‏حروف پیوسته: بسم‌الله الرحمن الرحیم" size 18
                
                null height 20
                
                text "English text sample:" size 20 color "#00aaff"
                text "Hello! This is a sample English text." size 18
                text "Numbers: 1234567890" size 18
                text "Punctuation: ? ! ; : \" '" size 18
                
                null height 20
                
                text "Mixed text sample:" size 20 color "#00ff00"
                text "‏English and فارسی mixed text example." size 18
                text "‏Version: نسخه 1.0 - Build 2025" size 18
            
            null height 30
            
            textbutton "‏بستن / Close":
                action Hide("persian_font_test")
                text_size 18
                xalign 0.5

# بلوک ۶: راه‌اندازی کلیدهای میانبر
init -996 python:
    # راه‌اندازی کلیدهای میانبر برای ویژگی‌های فارسی
    def setup_persian_shortcuts():
        """راه‌اندازی کلیدهای میانبر برای ویژگی‌های زبان فارسی"""
        
        # اضافه کردن میانبر منوی فارسی
        if 'persian_menu' not in config.keymap:
            config.keymap['persian_menu'] = ['shift_P', 'shift_p']
        
        # اضافه کردن میانبر تست فونت
        if 'persian_font_test' not in config.keymap:
            config.keymap['persian_font_test'] = ['shift_F', 'shift_f']
    
    setup_persian_shortcuts()

# بلوک ۷: ابزارهای کاربردی زبان فارسی
init -995 python:
    # توابع کاربردی برای کار با متن فارسی
    
    def is_persian_text(text):
        """بررسی اینکه آیا متن شامل حروف فارسی است"""
        if not isinstance(text, str):
            return False
        
        persian_range = range(0x0600, 0x06FF)  # محدوده یونیکد عربی/فارسی
        return any(ord(char) in persian_range for char in text)
    
    def format_persian_number(number):
        """تبدیل اعداد انگلیسی به اعداد فارسی"""
        english_to_persian = {
            '0': '۰', '1': '۱', '2': '۲', '3': '۳', '4': '۴',
            '5': '۵', '6': '۶', '7': '۷', '8': '۸', '9': '۹'
        }
        
        result = str(number)
        for eng, per in english_to_persian.items():
            result = result.replace(eng, per)
        return result
    
    def get_current_language_name():
        """دریافت نام نمایشی زبان فعلی"""
        lang = _preferences.language
        language_names = {
            None: "English",
            "fa": "‏فارسی",
            "es": "Español", 
            "fr": "Français",
            "ru": "Русский"
        }
        return language_names.get(lang, "Unknown")

# بلوک ۸: پیام موفقیت و تکمیل راه‌اندازی
init -994 python:
    # ثبت راه‌اندازی موفق
    try:
        print("Persian language support initialized successfully for Ren'Py 8.3.7")
        print(f"Preferred font: {preferred_font}")
        print(f"RTL enabled: {config.rtl}")
        print(f"Default language: {style.default.language}")
    except:
        pass

# برچسب راه‌اندازی خودکار پس از بارگذاری ذخیره‌ها
label after_load:
    # اطمینان از اعمال تنظیمات فارسی پس از بارگذاری
    python:
        if _preferences.language == "fa":
            config.rtl = True
            style.default.language = "fa"
    return

# برچسب تست سریع برای تغییر زبان
label test_persian:
    "Testing Persian language support..."
    "‏سلام! این یک تست زبان فارسی است."
    "‏آیا متن فارسی درست نمایش داده می‌شود؟"
    menu:
        "‏بله، عالی است!":
            "‏خوشحالم که کار می‌کند!"
        "‏نه، مشکل دارد":
            "‏لطفاً تنظیمات فونت را بررسی کنید."
    return

# دستور کنسول برای تغییر سریع زبان
init python:
    def quick_change_language(lang):
        """تغییر سریع زبان"""
        renpy.change_language(lang)
        renpy.restart_interaction()

    # اضافه کردن دستورات کنسول
    config.console_commands["persian"] = "quick_change_language('fa')"
    config.console_commands["english"] = "quick_change_language(None)"
    config.console_commands["lang_menu"] = "renpy.show_screen('persian_language_menu')"
