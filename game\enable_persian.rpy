# Comprehensive Persian Language & Engine Patch

# Block 1: Earliest-priority patch for Ren'Py engine functions.
# This runs before almost anything else to prevent crashes in game scripts.
init -999 python:
    import renpy

    # In some Ren'Py versions/mods, core functions are not attached to the
    # `renpy` module object during early init phases, causing crashes in
    # common library files (like 00layout.rpy). We patch them here.
    try:
        from renpy.exports import (
            has_screen, load_module, has_label, get_screen, get_widget, variant, list_files
        )

        if not hasattr(renpy, "has_screen"):
            renpy.has_screen = has_screen
        if not hasattr(renpy, "load_module"):
            renpy.load_module = load_module
        if not hasattr(renpy, "has_label"):
            renpy.has_label = has_label
        if not hasattr(renpy, "get_screen"):
            renpy.get_screen = get_screen
        if not hasattr(renpy, "get_widget"):
            renpy.get_widget = get_widget
        if not hasattr(renpy, "variant"):
            renpy.variant = variant
        if not hasattr(renpy, "list_files"):
            renpy.list_files = list_files
    except Exception as e:
        # This might fail if renpy.exports isn't ready, but it's worth a try.
        pass

# Block 2: Language and RTL configuration.
init -2 python:
    # Enable global RTL support for correct text direction.
    config.rtl = True

    # Set the default language for text shaping (ligatures and letter forms).
    # This tells Ren'Py's text engine (HarfBuzz) to apply Persian rules.
    style.default.language = "fa"

    # Set a high-quality Persian font as the default.
    # Place "Amiri-Regular.ttf" in the /game folder for the best result.
    preferred_font = "Amiri-Regular.ttf" if renpy.loader.loadable("Amiri-Regular.ttf") else "tahoma.ttf"
    style.default.font = preferred_font

    # Update the font replacement map to ensure all text uses the Persian font.
    config.font_replacement_map.update({
        ("DejaVuSans.ttf", False, False): (preferred_font, False, False),
        ("DejaVuSans.ttf", True, False): (preferred_font, True, False),
        ("DejaVuSans.ttf", False, True): (preferred_font, False, True),
        ("DejaVuSans.ttf", True, True): (preferred_font, True, True),
        ("Rubik-Regular.ttf", False, False): (preferred_font, False, False),
        ("Inter-Regular.ttf", False, False): (preferred_font, False, False),
        ("Hooverville.ttf", False, False): (preferred_font, False, False),
    })

# Block 3: Language registration and UI.
init python:
    # Add "fa" (Persian) to the list of known languages.
    def enable_persian():
        original_known = renpy.known_languages
        def new_known():
            langs = list(original_known())
            if "fa" not in langs:
                langs.append("fa")
            return langs
        renpy.known_languages = new_known
    enable_persian()

# Screen for language selection
screen persian_language_menu():
    modal True
    frame:
        xalign 0.5
        yalign 0.5
        xsize 500
        ysize 350
        vbox:
            spacing 15
            text "‏انتخاب زبان / Select Language" size 24 text_align 0.5 xalign 0.5
            null height 10
            textbutton "‏فارسی (Persian)" action [Language("fa"), Hide("persian_language_menu")] text_size 20 xalign 0.5
            textbutton "English" action [Language(None), Hide("persian_language_menu")] text_size 20 xalign 0.5
            textbutton "Español" action [Language("es"), Hide("persian_language_menu")] text_size 20 xalign 0.5
            null height 20
            text "‏کلیدهای میانبر / Shortcuts:" size 16 text_align 0.5 xalign 0.5
            text "Shift+P: Language Menu" size 14 text_align 0.5 xalign 0.5
            text "Shift+T: Font Test" size 14 text_align 0.5 xalign 0.5
            null height 10
            textbutton "‏بستن / Close" action Hide("persian_language_menu") text_size 18 xalign 0.5

# Key bindings for the menu
init:
    $ config.keymap['persian_menu'] = ['shift_P']
    $ config.underlay.append(renpy.Keymap(persian_menu=Show("persian_language_menu")))