# Advanced Persian Language Support for Ren'Py 8.3.7
# Comprehensive RTL and Persian text rendering support

# Block 1: Core RTL and Persian Configuration
init -1000 python:
    # Enable RTL (Right-to-Left) text direction globally
    config.rtl = True

    # Set Persian as the default language for text shaping
    # This enables proper Persian letter forms and ligatures
    style.default.language = "fa"

# Block 2: Font Configuration
init -999 python:
    # List of preferred Persian fonts in order of preference
    persian_fonts = [
        "Amiri-Regular.ttf",     # Best Arabic/Persian font
        "NotoSansArabic-Regular.ttf",
        "tahoma.ttf",            # Windows fallback
        "arial.ttf",             # Universal fallback
        "DejaVuSans.ttf"         # Ren'Py default
    ]

    # Find the best available font
    preferred_font = "DejaVuSans.ttf"  # Default fallback
    for font in persian_fonts:
        if renpy.loader.loadable(font):
            preferred_font = font
            break

    # Apply the font to default style
    style.default.font = preferred_font

    # Create comprehensive font replacement map
    # This ensures ALL text uses the Persian font
    font_replacements = {}
    default_fonts = [
        "DejaVuSans.ttf", "Rubik-Regular.ttf", "Inter-Regular.ttf",
        "Hooverville.ttf", "arial.ttf", "calibri.ttf"
    ]

    for font in default_fonts:
        for bold in [False, True]:
            for italic in [False, True]:
                font_replacements[(font, bold, italic)] = (preferred_font, bold, italic)

    config.font_replacement_map.update(font_replacements)

# Block 3: Persian Text Processing
init -998 python:
    # Persian text processing utilities
    def process_persian_text(text):
        """Process Persian text for better rendering"""
        if not isinstance(text, str):
            return text

        # Add ZWNJ (Zero Width Non-Joiner) where needed for better text flow
        # This helps with Persian text rendering
        import re

        # Add proper spacing for Persian punctuation
        text = re.sub(r'([؟!:؛])([^\s])', r'\1 \2', text)
        text = re.sub(r'([^\s])([؟!:؛])', r'\1 \2', text)

        return text

    # Make the function available globally for manual use
    store.process_persian_text = process_persian_text

# Block 4: Language Support
init -997 python:
    # Persian language configuration
    def setup_persian_language():
        """Setup Persian language support"""
        # Ensure Persian is recognized as a valid language
        # The translation system will automatically detect it if tl/fa/ exists

        # Configure Persian-specific settings
        if hasattr(config, 'language_callbacks'):
            def persian_callback():
                # Called when switching to Persian
                style.default.language = "fa"
                config.rtl = True

            config.language_callbacks.setdefault("fa", []).append(persian_callback)

        # Set up default Persian preferences
        if not hasattr(store, '_persian_setup_done'):
            store._persian_setup_done = True

            # Create Persian language directory if it doesn't exist
            import os
            persian_dir = os.path.join(config.gamedir, "tl", "fa")
            if not os.path.exists(persian_dir):
                try:
                    os.makedirs(persian_dir, exist_ok=True)
                    # Create a basic translation file to register the language
                    with open(os.path.join(persian_dir, "common.rpy"), "w", encoding="utf-8") as f:
                        f.write('# Persian translation file\n')
                        f.write('translate fa strings:\n')
                        f.write('    old "New Game"\n')
                        f.write('    new "بازی جدید"\n')
                except:
                    pass  # Ignore errors if we can't create directories

    setup_persian_language()

# Block 5: Persian Language Menu Screen
screen persian_language_menu():
    # Advanced Persian language selection menu
    modal True

    # Background overlay
    add "#000000aa"

    # Main menu frame
    frame:
        xalign 0.5
        yalign 0.5
        xsize 600
        ysize 450
        padding (30, 30)

        vbox:
            spacing 20

            # Title
            text "‏انتخاب زبان / Language Selection" size 28 text_align 0.5 xalign 0.5 color "#ffffff"

            null height 20

            # Language buttons
            vbox:
                spacing 15
                xalign 0.5

                textbutton "‏فارسی (Persian)":
                    action [Language("fa"), Hide("persian_language_menu")]
                    text_size 22
                    xalign 0.5
                    xsize 400

                textbutton "English":
                    action [Language(None), Hide("persian_language_menu")]
                    text_size 22
                    xalign 0.5
                    xsize 400

                textbutton "Español":
                    action [Language("es"), Hide("persian_language_menu")]
                    text_size 22
                    xalign 0.5
                    xsize 400

                textbutton "Français":
                    action [Language("fr"), Hide("persian_language_menu")]
                    text_size 22
                    xalign 0.5
                    xsize 400

                textbutton "Русский":
                    action [Language("ru"), Hide("persian_language_menu")]
                    text_size 22
                    xalign 0.5
                    xsize 400

            null height 30

            # Instructions
            vbox:
                spacing 8
                xalign 0.5

                text "‏راهنمای کلیدهای میانبر / Keyboard Shortcuts:" size 16 text_align 0.5 xalign 0.5 color "#cccccc"
                text "Shift+P: Open Language Menu" size 14 text_align 0.5 xalign 0.5 color "#aaaaaa"
                text "Shift+F: Font Test Screen" size 14 text_align 0.5 xalign 0.5 color "#aaaaaa"
                text "Escape: Close Menu" size 14 text_align 0.5 xalign 0.5 color "#aaaaaa"

            null height 20

            # Close button
            textbutton "‏بستن / Close":
                action Hide("persian_language_menu")
                text_size 20
                xalign 0.5
                xsize 200

# Block 6: Font Test Screen
screen persian_font_test():
    # Screen to test Persian font rendering
    modal True

    add "#000000dd"

    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 600
        padding (40, 40)

        vbox:
            spacing 20

            text "‏تست فونت فارسی / Persian Font Test" size 24 text_align 0.5 xalign 0.5

            null height 20

            # Persian text samples
            vbox:
                spacing 15

                text "‏نمونه متن فارسی:" size 20 color "#ffaa00"
                text "‏سلام! این یک متن نمونه فارسی است." size 18
                text "‏اعداد: ۱۲۳۴۵۶۷۸۹۰" size 18
                text "‏علائم نگارشی: ؟ ! ؛ : « »" size 18
                text "‏حروف پیوسته: م" size 18

                null height 20

                text "English text sample:" size 20 color "#00aaff"
                text "Hello! This is a sample English text." size 18
                text "Numbers: 1234567890" size 18
                text "Punctuation: ? ! ; : \" '" size 18

                null height 20

                text "Mixed text sample:" size 20 color "#00ff00"
                text "‏English and فارسی mixed text example." size 18
                text "‏Version: نسخه 1.0 - Build 2025" size 18

            null height 30

            textbutton "‏بستن / Close":
                action Hide("persian_font_test")
                text_size 18
                xalign 0.5

# Block 7: Keyboard Shortcuts Setup
init -996 python:
    # Setup keyboard shortcuts for Persian features
    def setup_persian_shortcuts():
        """Setup keyboard shortcuts for Persian language features"""

        # Add Persian menu shortcut
        if 'persian_menu' not in config.keymap:
            config.keymap['persian_menu'] = ['shift_P', 'shift_p']

        # Add font test shortcut
        if 'persian_font_test' not in config.keymap:
            config.keymap['persian_font_test'] = ['shift_F', 'shift_f']

        # Create keymap actions using a compatible method
        def show_persian_menu():
            renpy.show_screen("persian_language_menu")

        def show_font_test():
            renpy.show_screen("persian_font_test")

        # Store the functions for keymap use
        store.show_persian_menu = show_persian_menu
        store.show_font_test = show_font_test

        # Add to keymap using a compatible approach
        try:
            # Try the modern approach first
            from renpy.display.behavior import Keymap
            persian_keymap = Keymap(
                persian_menu=show_persian_menu,
                persian_font_test=show_font_test
            )
            config.underlay.append(persian_keymap)
        except:
            # Fallback: add to keymap directly
            config.keymap.update({
                'persian_menu': ['shift_P'],
                'persian_font_test': ['shift_F']
            })

    setup_persian_shortcuts()

# Block 8: Persian Language Utilities
init -995 python:
    # Utility functions for Persian text handling

    def is_persian_text(text):
        """Check if text contains Persian characters"""
        if not isinstance(text, str):
            return False

        persian_range = range(0x0600, 0x06FF)  # Arabic/Persian Unicode range
        return any(ord(char) in persian_range for char in text)

    def format_persian_number(number):
        """Convert English numbers to Persian numbers"""
        english_to_persian = {
            '0': '۰', '1': '۱', '2': '۲', '3': '۳', '4': '۴',
            '5': '۵', '6': '۶', '7': '۷', '8': '۸', '9': '۹'
        }

        result = str(number)
        for eng, per in english_to_persian.items():
            result = result.replace(eng, per)
        return result

    def get_current_language_name():
        """Get the display name of current language"""
        lang = _preferences.language
        language_names = {
            None: "English",
            "fa": "‏فارسی",
            "es": "Español",
            "fr": "Français",
            "ru": "Русский"
        }
        return language_names.get(lang, "Unknown")

    # Make utilities available globally
    store.is_persian_text = is_persian_text
    store.format_persian_number = format_persian_number
    store.get_current_language_name = get_current_language_name

# Block 9: Success message and initialization complete
init -994 python:
    # Log successful initialization
    try:
        print("Persian language support initialized successfully for Ren'Py 8.3.7")
        print(f"Preferred font: {preferred_font}")
        print(f"RTL enabled: {config.rtl}")
        print(f"Default language: {style.default.language}")
    except:
        pass

# Auto-setup label for after loading saves
label after_load:
    # Ensure Persian settings are applied after loading
    python:
        if _preferences.language == "fa":
            config.rtl = True
            style.default.language = "fa"
    return